import { OAuth2Client } from "google-auth-library";
import { initializeApp, cert } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getMessaging } from "firebase-admin/messaging";

const firebaseServiceAccount = {
    "type": "service_account",
    "project_id": "alienai-id",
    "private_key_id": "28fa8aff051d632ac5797cc250c5779f73cf4b2e",
    "private_key":
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    "client_email": "<EMAIL>",
    "client_id": "111421287394114152445",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    "client_x509_cert_url":
        "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40alienai-id.iam.gserviceaccount.com",
    "universe_domain": "googleapis.com",
};

const cred = cert(firebaseServiceAccount as any);
// Initialize Firebase Admin SDK
const firebaseApp = initializeApp({
    credential: cred,
});

const firebaseMessaging = getMessaging(firebaseApp);

const firebaseAuth = getAuth(firebaseApp);

export async function verifyFirebaseIdToken(token: string) {
    return await firebaseAuth.verifyIdToken(token);
}

const client = new OAuth2Client(); // No need for clientId here

export async function verifyGoogleIdToken(clientId: string, idToken: string) {
    const ticket = await client.verifyIdToken({
        idToken,
        audience: clientId, // Specify the client ID of your app
    });

    const payload = ticket.getPayload();
    // console.log("User info:", payload);

    return payload; // Contains sub (userId), email, name, picture, etc.
}

export async function sendFirebaseNotification(token: string, notif: Notif) {
    return firebaseMessaging.send({
        token: token,
        notification: {
            title: notif.title,
            body: notif.body,
        },
        data: notif.data,
    });
}
