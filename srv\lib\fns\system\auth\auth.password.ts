import { $err } from "cm_net";
import { gatewayStore } from "../../../../../instance";
import { $fn } from "../../../../core/fn";
import { checkSaltedPassword, saltPassword } from "../../../../core/utils/user/password";

$fn("auth.pass", async function (email: string, pass: string) {
    const user = await gatewayStore.userGetByEmail(email);
    if (!user)  throw $err.notFoundAsset("user", 'email', email); 
    if (!user.pass)  throw $err.err("user-no-pass", "User does not have a password"); 


    const isValid = await checkSaltedPassword(pass, user.pass);
    if (!isValid) throw $err.err("user-wrong-pass", "Wrong password");
    
    this.user.uid = user.id;
});

$fn("auth.pass.set", async function (id: string, oldPass: string, newPass: string) {
    const user = await gatewayStore.userGetById(id);
    if (!user)  throw $err.notFoundAsset("user", 'User', id); 
    if (!user.pass)  throw $err.err("user-no-pass", "User does not have a password"); 

    const isValid = await checkSaltedPassword(oldPass, user.pass);
    if (!isValid) throw $err.err("user-wrong-pass", "Wrong password");

    const newSalted = await saltPassword(newPass);
    await gatewayStore.userSet({
        id: user.id,
        pass: newSalted,
    });
});