apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: redis
  labels:
    app: redis
data:
  redis.conf: |
    # Redis configuration for production use
    
    # Network
    bind 0.0.0.0
    port 6379
    protected-mode yes
    
    # General
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    databases 16
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir /data
    
    # AOF (Append Only File)
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    aof-load-truncated yes
    
    # Memory management
    maxmemory-policy allkeys-lru
    
    # Security
    requirepass REDIS_PASSWORD_PLACEHOLDER
    
    # Slow log
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    
    # Client output buffer limits
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
    
    # TCP keepalive
    tcp-keepalive 300
    
    # Timeout
    timeout 0
