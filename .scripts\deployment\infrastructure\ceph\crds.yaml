apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: cephclusters.ceph.rook.io
spec:
  group: ceph.rook.io
  names:
    kind: CephCluster
    listKind: CephClusterList
    plural: cephclusters
    singular: cephcluster
  scope: Namespaced
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              cephVersion:
                type: object
                properties:
                  image:
                    type: string
                  allowUnsupported:
                    type: boolean
              dataDirHostPath:
                type: string
              mon:
                type: object
                properties:
                  count:
                    type: integer
                  allowMultiplePerNode:
                    type: boolean
              dashboard:
                type: object
                properties:
                  enabled:
                    type: boolean
                  ssl:
                    type: boolean
              monitoring:
                type: object
                properties:
                  enabled:
                    type: boolean
              network:
                type: object
                properties:
                  hostNetwork:
                    type: boolean
              storage:
                type: object
                properties:
                  useAllNodes:
                    type: boolean
                  useAllDevices:
                    type: boolean
                  config:
                    type: object
                    properties:
                      osdsPerDevice:
                        type: string
                  directories:
                    type: array
                    items:
                      type: object
                      properties:
                        path:
                          type: string
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: cephblockpools.ceph.rook.io
spec:
  group: ceph.rook.io
  names:
    kind: CephBlockPool
    listKind: CephBlockPoolList
    plural: cephblockpools
    singular: cephblockpool
  scope: Namespaced
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              failureDomain:
                type: string
              replicated:
                type: object
                properties:
                  size:
                    type: integer
                  requireSafeReplicaSize:
                    type: boolean
