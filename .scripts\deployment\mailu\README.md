# Mailu Mail Server Deployment

This directory contains the configuration and scripts to deploy Mailu mail server on Kubernetes with Ceph persistent storage.

## 🎯 Overview

Mailu is a simple yet full-featured mail server as a set of Docker images. This deployment includes:

- **Mail Server Components**: SMTP, IMAP, POP3, Webmail (Roundcube)
- **Security**: Antispam (Rspamd), Antivirus (ClamAV), DKIM signing
- **Administration**: Web-based admin interface
- **Storage**: Persistent storage using Ceph via Rook
- **SSL/TLS**: Automatic certificate management via cert-manager
- **Ingress**: Traefik ingress controller integration

## 📁 Files

- `namespace.yaml` - Kubernetes namespace definition
- `values.yaml` - Helm chart values configuration
- `deploy-mailu.sh` - Deployment script
- `README.md` - This documentation

## 🔧 Prerequisites

Before deploying Mailu, ensure you have:

1. **Kubernetes cluster** with sufficient resources
2. **Ceph storage** deployed and configured (rook-ceph-block storage class)
3. **Helm 3.x** installed
4. **cert-manager** installed (for SSL certificates)
5. **Traefik** ingress controller installed
6. **Domain name** configured and DNS access

### Resource Requirements

Minimum recommended resources:
- **CPU**: 2-4 cores
- **Memory**: 4-8 GB RAM
- **Storage**: 100+ GB (depending on mail volume)

## 🚀 Quick Start

### 1. Customize Configuration

Edit `values.yaml` and update the following **REQUIRED** settings:

```yaml
global:
  domain: "your-domain.com"  # Change to your domain
  secretKey: "your-32-char-secret-key"  # Generate a random 32-character string

admin:
  initialAccount:
    username: "admin"
    domain: "your-domain.com"  # Change to your domain
    password: "secure-admin-password"  # Set a secure password

ingress:
  hosts:
    - host: "mail.your-domain.com"  # Change to your mail subdomain
  tls:
    - hosts:
        - "mail.your-domain.com"  # Change to your mail subdomain
```

### 2. Deploy Mailu

Run the deployment script:

```bash
chmod +x .scripts/deployment/mailu/deploy-mailu.sh
.scripts/deployment/mailu/deploy-mailu.sh
```

### 3. Configure DNS

Set up the following DNS records:

```
# A record for mail server
mail.your-domain.com.    IN  A     <your-cluster-ip>

# MX record for email delivery
your-domain.com.         IN  MX    10 mail.your-domain.com.

# SPF record for spam protection
your-domain.com.         IN  TXT   "v=spf1 mx ~all"

# DMARC record for email authentication
_dmarc.your-domain.com.  IN  TXT   "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
```

## 📧 Access Information

After successful deployment:

- **Web Admin**: `https://mail.your-domain.com/admin`
- **Webmail**: `https://mail.your-domain.com`
- **SMTP**: `mail.your-domain.com:587` (STARTTLS)
- **IMAP**: `mail.your-domain.com:993` (SSL/TLS)
- **POP3**: `mail.your-domain.com:995` (SSL/TLS)

## 🔧 Configuration Options

### Storage Configuration

The deployment uses Ceph storage with the following default sizes:

```yaml
persistence:
  size: "50Gi"              # Main mail data
  attachments:
    size: "100Gi"           # Large attachments and files

postgresql:
  persistence:
    size: "20Gi"            # Database storage

redis:
  persistence:
    size: "5Gi"             # Cache storage
```

### Security Features

- **Antispam**: Rspamd with machine learning
- **Antivirus**: ClamAV virus scanning
- **DKIM**: Automatic DKIM signing
- **TLS**: Enforced encryption for all connections
- **Authentication**: SMTP authentication required

### Resource Limits

Default resource limits are conservative. For production use, consider increasing:

```yaml
imap:
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
```

## 🛠️ Management

### Check Deployment Status

```bash
# Check all pods
kubectl get pods -n mailu

# Check services
kubectl get svc -n mailu

# Check persistent volumes
kubectl get pvc -n mailu

# Check ingress
kubectl get ingress -n mailu
```

### View Logs

```bash
# View logs for specific component
kubectl logs -n mailu deployment/mailu-admin
kubectl logs -n mailu deployment/mailu-smtp
kubectl logs -n mailu deployment/mailu-imap

# Follow logs in real-time
kubectl logs -f -n mailu deployment/mailu-admin
```

### Scale Components

```bash
# Scale webmail for high load
kubectl scale deployment mailu-webmail --replicas=3 -n mailu

# Scale IMAP servers
kubectl scale deployment mailu-imap --replicas=2 -n mailu
```

## 🔄 Updates

To update Mailu to a newer version:

```bash
# Update Helm repository
helm repo update

# Upgrade deployment
helm upgrade mailu mailu/mailu \
    --namespace mailu \
    --values .scripts/deployment/mailu/values.yaml
```

## 🗑️ Uninstall

To completely remove Mailu:

```bash
# Remove Helm deployment
helm uninstall mailu -n mailu

# Remove persistent volumes (WARNING: This deletes all mail data!)
kubectl delete pvc -n mailu --all

# Remove namespace
kubectl delete namespace mailu
```

## 🐛 Troubleshooting

### Common Issues

1. **Pods stuck in Pending**: Check if Ceph storage class is available
2. **SSL certificate issues**: Verify cert-manager is working
3. **Email not sending**: Check DNS MX records and firewall rules
4. **High memory usage**: Increase resource limits for antivirus component

### Debug Commands

```bash
# Check storage class
kubectl get storageclass rook-ceph-block

# Check cert-manager
kubectl get clusterissuer

# Check Traefik ingress
kubectl get ingressroute -A

# Test email connectivity
kubectl exec -it -n mailu deployment/mailu-admin -- nc -zv smtp.gmail.com 587
```

## 📚 Additional Resources

- [Mailu Documentation](https://mailu.io/)
- [Mailu Helm Chart](https://github.com/Mailu/helm-charts)
- [Email Security Best Practices](https://mailu.io/master/faq.html#how-to-setup-spf-dkim-and-dmarc)

## 🔐 Security Considerations

1. **Change default passwords** in values.yaml
2. **Use strong secret keys** (32+ characters)
3. **Enable firewall rules** for mail ports (25, 587, 993, 995)
4. **Configure backup strategy** for mail data
5. **Monitor logs** for suspicious activity
6. **Keep components updated** regularly
