kind: ServiceAccount
apiVersion: v1
metadata:
  name: tidb-controller-manager
  namespace: tidb-admin
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: tidb-controller-manager
rules:
- apiGroups: ["pingcap.com"]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: [""]
  resources: ["pods", "services", "persistentvolumeclaims", "persistentvolumes"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["statefulsets", "deployments"]
  verbs: ["*"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["*"]
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["*"]
- apiGroups: [""]
  resources: ["configmaps", "endpoints", "events", "secrets"]
  verbs: ["*"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: tidb-controller-manager
subjects:
- kind: ServiceAccount
  name: tidb-controller-manager
  namespace: tidb-admin
roleRef:
  kind: ClusterRole
  name: tidb-controller-manager
  apiGroup: rbac.authorization.k8s.io
