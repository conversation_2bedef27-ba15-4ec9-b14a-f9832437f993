import { gatewayStore } from "../../../../instance";
import { WSHandlerPart } from "./ws.handler._part";

export class WSHandlerVars extends WSHandlerPart {
    private _vars: { [key: string]: any } = {};
    get(key: string): any {
        return this._vars[key];
    }
    async set(key: string, value: any) {
        await this.sets({ [key]: value });
    }

    async sets(setters: { [key: string]: any }) {
        for (const [k, v] of Object.entries(setters)) {
            if (typeof v === "undefined") {
                delete this._vars[k];
            } else {
                this._vars[k] = v;
            }
        }

        await gatewayStore.sessionSet({
            ...this._vars,
            id: this.wsHandler.sid,
        });
    }

    async load() {
        const vars = await gatewayStore.sessionGet(this.wsHandler.sid);
        this._vars = vars;
    }

    async updateLastActive() {
        await gatewayStore.sessionSet({
            id: this.wsHandler.sid,
            lastOnline: new Date(),
        });
    }
}
