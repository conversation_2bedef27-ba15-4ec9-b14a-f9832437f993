apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-simple
  namespace: redis
  labels:
    app: redis-simple
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-simple
  template:
    metadata:
      labels:
        app: redis-simple
    spec:
      containers:
      - name: redis
        image: redis:6
        ports:
        - containerPort: 6379
          name: redis
        args:
        - redis-server
        - --requirepass
        - "Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: redis-simple
  namespace: redis
  labels:
    app: redis-simple
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis-simple
---
apiVersion: v1
kind: Service
metadata:
  name: redis-simple-nodeport
  namespace: redis
  labels:
    app: redis-simple
spec:
  type: NodePort
  ports:
  - port: 6379
    targetPort: 6379
    nodePort: 32380
    protocol: TCP
    name: redis
  selector:
    app: redis-simple
