import { gatewayStore } from "../../../../instance";
import { $fn } from "../../../core/fn";

$fn('user.device.add', async function (uid: string, sid: string, name: string) {
    await gatewayStore.userDeviceAdd(uid, sid, name);
});

$fn('user.device.del', async function (uid: string, sid: string) {
    await gatewayStore.userDeviceDel(uid, sid);
});

$fn('device.access.list', async function (uid: string) {
    return await gatewayStore.userDeviceList(uid);
});

$fn('device.users.list', async function (sid: string) {
    return await gatewayStore.deviceUserList(sid);
});
