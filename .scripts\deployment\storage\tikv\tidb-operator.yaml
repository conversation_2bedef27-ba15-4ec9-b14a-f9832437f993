apiVersion: v1
kind: Namespace
metadata:
  name: tidb-operator

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tidb-controller-manager
  namespace: tidb-operator

---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: tidb-operator
rules:
- apiGroups: [""]
  resources: ["pods", "services", "events", "persistentvolumeclaims", "secrets", "configmaps", "endpoints"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments", "statefulsets"]
  verbs: ["*"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["*"]
- apiGroups: ["pingcap.com"]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["*"]
- apiGroups: ["extensions"]
  resources: ["ingresses"]
  verbs: ["*"]
- apiGroups: ["apps.pingcap.com"]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["apiextensions.k8s.io"]
  resources: ["customresourcedefinitions"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
- apiGroups: ["storage.k8s.io"]
  resources: ["storageclasses"]
  verbs: ["get", "list", "watch"]

---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: tidb-operator
subjects:
- kind: ServiceAccount
  name: tidb-controller-manager
  namespace: tidb-operator
roleRef:
  kind: ClusterRole
  name: tidb-operator
  apiGroup: rbac.authorization.k8s.io

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: tidb-controller-manager
  namespace: tidb-operator
  labels:
    app.kubernetes.io/name: tidb-operator
    app.kubernetes.io/component: controller-manager
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: tidb-operator
      app.kubernetes.io/component: controller-manager
  template:
    metadata:
      labels:
        app.kubernetes.io/name: tidb-operator
        app.kubernetes.io/component: controller-manager
    spec:
      serviceAccount: tidb-controller-manager
      containers:
      - name: tidb-operator
        image: pingcap/tidb-operator:v1.5.0
        imagePullPolicy: IfNotPresent
        command:
        - /usr/local/bin/tidb-controller-manager
        - -v=2
        - -cluster-scoped=true
        env:
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: TZ
          value: UTC
        resources:
          limits:
            cpu: 500m
            memory: 500Mi
          requests:
            cpu: 200m
            memory: 200Mi
