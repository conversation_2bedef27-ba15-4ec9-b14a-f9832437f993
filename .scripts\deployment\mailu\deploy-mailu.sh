#!/bin/bash
set -e

echo "🚀 Deploying Mailu Mail Server with Ceph persistent storage..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if helm is installed
if ! command -v helm &> /dev/null; then
    print_error "Helm is not installed. Please install Helm first."
    exit 1
fi

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed. Please install kubectl first."
    exit 1
fi

# Check if Ceph storage class exists
print_status "Checking if Ceph storage class exists..."
if ! kubectl get storageclass rook-ceph-block &> /dev/null; then
    print_error "Ceph storage class 'rook-ceph-block' not found. Please deploy Ceph first."
    print_status "Run: .scripts/deployment/deploy-ceph.sh"
    exit 1
fi

print_success "Ceph storage class found"

# Check if cert-manager is installed
print_status "Checking if cert-manager is installed..."
if ! kubectl get namespace cert-manager &> /dev/null; then
    print_warning "cert-manager namespace not found. SSL certificates may not work."
    print_status "Consider installing cert-manager for automatic SSL certificates."
else
    print_success "cert-manager found"
fi

# Add Mailu Helm repository
print_status "Adding Mailu Helm repository..."
helm repo add mailu https://mailu.github.io/helm-charts
helm repo update

print_success "Mailu Helm repository added and updated"

# Create namespace
print_status "Creating Mailu namespace..."
kubectl apply -f .scripts/deployment/mailu/namespace.yaml

print_success "Mailu namespace created"

# Wait for namespace to be ready
print_status "Waiting for namespace to be ready..."
kubectl wait --for=condition=Ready namespace/mailu --timeout=60s

# Check if values.yaml has been customized
print_warning "⚠️  IMPORTANT: Please customize the values in .scripts/deployment/mailu/values.yaml before deployment!"
print_warning "   - Change the domain to your actual domain"
print_warning "   - Set a secure secret key"
print_warning "   - Set secure admin credentials"
print_warning ""

read -p "Have you customized the values.yaml file? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_error "Please customize the values.yaml file first, then run this script again."
    exit 1
fi

# Deploy Mailu using Helm
print_status "Deploying Mailu mail server..."
helm upgrade --install mailu mailu/mailu \
    --namespace mailu \
    --values .scripts/deployment/mailu/values.yaml \
    --timeout 10m \
    --wait

print_success "Mailu deployment initiated"

# Wait for deployments to be ready
print_status "Waiting for Mailu components to be ready..."

# List of deployments to wait for
deployments=(
    "mailu-admin"
    "mailu-front"
    "mailu-imap"
    "mailu-smtp"
    "mailu-antispam"
    "mailu-webmail"
)

for deployment in "${deployments[@]}"; do
    print_status "Waiting for $deployment to be ready..."
    kubectl wait --for=condition=Available deployment/$deployment -n mailu --timeout=300s || {
        print_warning "Deployment $deployment is taking longer than expected"
    }
done

# Wait for StatefulSets (database, redis)
statefulsets=(
    "mailu-postgresql"
    "mailu-redis"
)

for sts in "${statefulsets[@]}"; do
    print_status "Waiting for $sts to be ready..."
    kubectl wait --for=condition=Ready statefulset/$sts -n mailu --timeout=300s || {
        print_warning "StatefulSet $sts is taking longer than expected"
    }
done

print_success "Mailu deployment completed!"

# Display status information
echo ""
print_status "📊 Deployment Status:"
kubectl get pods -n mailu
echo ""

print_status "💾 Storage Status:"
kubectl get pvc -n mailu
echo ""

print_status "🌐 Service Status:"
kubectl get svc -n mailu
echo ""

print_status "🔗 Ingress Status:"
kubectl get ingress -n mailu
echo ""

# Display access information
echo ""
print_success "🎉 Mailu has been successfully deployed!"
echo ""
print_status "📧 Access Information:"
print_status "   Web Admin: https://mail.alienai.id/admin"
print_status "   Webmail:   https://mail.alienai.id"
print_status "   SMTP:      mail.alienai.id:587 (STARTTLS)"
print_status "   IMAP:      mail.alienai.id:993 (SSL/TLS)"
print_status "   POP3:      mail.alienai.id:995 (SSL/TLS)"
echo ""
print_warning "⚠️  Remember to:"
print_warning "   1. Configure your DNS records to point to your cluster"
print_warning "   2. Update your domain's MX records"
print_warning "   3. Configure SPF, DKIM, and DMARC records"
print_warning "   4. Test email sending and receiving"
echo ""

# Show logs if there are any issues
failed_pods=$(kubectl get pods -n mailu --field-selector=status.phase!=Running --no-headers 2>/dev/null | wc -l)
if [ "$failed_pods" -gt 0 ]; then
    print_warning "Some pods are not running. Checking logs..."
    kubectl get pods -n mailu --field-selector=status.phase!=Running
    echo ""
    print_status "To check logs for a specific pod, run:"
    print_status "kubectl logs -n mailu <pod-name>"
fi

print_success "Deployment script completed!"
