import { $err } from "cm_net";
import { $fn } from "../../../../core/fn";
import { verifyGoogleIdToken } from "../../../../core/utils/user/user.auth";
import { gatewayStore } from "../../../../../instance";


$fn("auth.google", async function (clientId: string, idToken: string) {
    const decodedToken = await verifyGoogleIdToken(
        clientId,
        idToken,
    );
    const email = decodedToken?.email;
    const name = decodedToken?.name;
    const pic = decodedToken?.profile;

    if (!email || !name) {
        throw $err.err("user-unverified", "Google profile is not verified");
    }

    const user = await gatewayStore.userGetOrCreateByEmail({
        email: email,
        name: name,
        pic: pic,
    });

    this.user.uid = user.id;
});
