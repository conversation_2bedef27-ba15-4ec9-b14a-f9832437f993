apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-data
  namespace: redis
  labels:
    app: redis
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: rook-ceph-block
  resources:
    requests:
      storage: 20Gi  # Adjust size based on your needs
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-config
  namespace: redis
  labels:
    app: redis
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: rook-ceph-block
  resources:
    requests:
      storage: 1Gi  # For configuration and logs
