// deno-lint-ignore-file no-explicit-any
export type SubscribtionCallback<T = any> = (data: T) => void;
export type SubscribtionCancel = () => void;


export abstract class PubSubBackend {

    // -------------------------------------------------- connection
    abstract connect(): Promise<any>;
    abstract disconnect(): Promise<any>;

    // -------------------------------------------------- publish / subscribe
    abstract subscribe(topic: string, cb: SubscribtionCallback): SubscribtionCancel;
    abstract publish(topic: string, data: any): Promise<any>;
}

export class PubSub<T = any> {
    readonly backend: PubSubBackend;
    readonly subscribers: { [topic: string]: Set<SubscribtionCallback<T>> } = {};
    readonly subscribeCancels: { [topic: string]: SubscribtionCancel } = {};

    constructor(backend: PubSubBackend) {
        this.backend = backend;
    }

    // -------------------------------------------------- connection
    connect(): Promise<any> { return this.backend.connect(); }
    disconnect(): Promise<any> { return this.backend.disconnect(); }

    // -------------------------------------------------- publish / subscribe
    subscribe(topic: string, handler: SubscribtionCallback<T>): SubscribtionCancel {
        if (!this.subscribers[topic]) {
            this.subscribers[topic] = new Set();

            // subscribe to backend
            this.subscribeCancels[topic] = this.backend.subscribe(topic, (data) => {
                for (const handler of this.subscribers[topic]) { handler(data); }
            });
        }
        this.subscribers[topic].add(handler);
        return () => {
            this.subscribers[topic].delete(handler);

            // unsubscribe from backend
            if (this.subscribers[topic].size === 0) {
                delete this.subscribers[topic];
                
                const cancel = this.subscribeCancels[topic];
                delete this.subscribeCancels[topic];
                cancel();
            }
        }
    }

    publish(topic: string, data: T): Promise<any> {
        return this.backend.publish(topic, data);
    }

}