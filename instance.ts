import { useSDB } from "cm_sdb";
import {
    OLLAMA_URL,
    REDIS_URL,
    // REDIS_URL,
    SURREAL_PASS,
    SURREAL_URL,
    SURREAL_USER,
} from "./config";
import { GatewayStoreSDB } from "./srv/core/utils/store/gateway.store.sdb";
import type { SDBCallback } from "cm_sdb";
import { PubSubRedis } from "./srv/core/utils/pubsub/pubsub.redis";

// ------------------------ Pubsub
export const pubSub = new PubSubRedis(REDIS_URL);
await pubSub.connect();

export const gatewayStore = new GatewayStoreSDB({
    ollamaUrl: OLLAMA_URL,
    url: SURREAL_URL,
    ns: "cm",
    db: "gateway",
    user: SURREAL_USER,
    pass: SURREAL_PASS,
});
await gatewayStore.connect();

export function useGatewaySDB<T = any>(
    db: string,
    cb: SDBCallback<T>,
): Promise<T> {
    return useSDB({
        ollamaUrl: OLLAMA_URL,
        url: SURREAL_URL,
        ns: "cm",
        db: db,
        user: SURREAL_USER,
        pass: SURREAL_PASS,
    }, cb);
}
