apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: mongodb
type: Opaque
data:
  # Base64 encoded credentials
  # admin
  mongodb-root-username: YWRtaW4=
  # Mg8Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8
  mongodb-root-password: TWc4S3g5UHEyTnY3V3I1WnQzQm02THAySGoxUXc4
  # cm_app_user  
  mongodb-username: Y21fYXBwX3VzZXI=
  # Yx4Kp9Mq7Nv2Wr8Zt6Bm3Lp1Hj5Qw9
  mongodb-password: WXg0S3A5TXE3TnYyV3I4WnQ2Qm0zTHAxSGo1UXc5
  # cm_app_db
  mongodb-database: Y21fYXBwX2Ri
