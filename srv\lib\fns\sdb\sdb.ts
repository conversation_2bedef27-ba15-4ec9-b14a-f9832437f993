import { type DelRangeOpts, type M, type QTbl } from "cm_sdb";
import { $fn } from "../../../core/fn";
import { useGatewaySDB } from "../../../../instance";

$fn("sdb.put", async function (db: string, tbl: string, doc: M | M[]) {
    return useGatewaySDB(db, (sdb) => {
        return sdb.put(tbl, doc);
    });
});

$fn("sdb.del", async function (db: string, tbl: string, id: string) {
    return useGatewaySDB(db, (sdb) => {
        return sdb.del(tbl, id);
    });
});

$fn("sdb.get", async function (db: string, tbl: string, id: string) {
    return useGatewaySDB(db, (sdb) => {
        return sdb.get(tbl, id);
    });
});

$fn("sdb.q", async function (db: string, q: QTbl) {
    return useGatewaySDB(db, (sdb) => {
        return sdb.q(q);
    });
});

$fn('sdb.del.range', async function (db: string, tbl: string, opts: DelRangeOpts) {
    return useGatewaySDB(db, (sdb) => {
        return sdb.delRange(tbl, opts);
    });
})