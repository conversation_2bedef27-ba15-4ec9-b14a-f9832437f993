#!/bin/bash
set -e

echo "🐳 Deploying Docker Registry with Ceph Storage and Traefik Ingress"
echo "=================================================================="

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Check if cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Cannot connect to Kubernetes cluster"
    exit 1
fi

# Check if Ceph storage class exists
echo "🔍 Checking if Ceph storage class exists..."
if ! kubectl get storageclass rook-ceph-block &> /dev/null; then
    echo "❌ rook-ceph-block storage class not found"
    echo "Please deploy Ceph first using: .scripts/deployment/deploy-ceph.sh"
    exit 1
fi

# Check if cert-manager cluster issuer exists
echo "🔍 Checking if cert-manager cluster issuer exists..."
if ! kubectl get clusterissuer letsencrypt-prod &> /dev/null; then
    echo "⚠️  letsencrypt-prod cluster issuer not found"
    echo "Deploying cluster issuer..."
    kubectl apply -f .scripts/deployment/cluster-issuer.yaml
fi

# Deploy Docker Registry
echo "🚀 Deploying Docker Registry..."
kubectl apply -f .scripts/deployment/docker-registry.yaml

# Wait for namespace to be created
echo "⏳ Waiting for namespace to be ready..."
kubectl wait --for=condition=Active namespace/docker-registry --timeout=60s

# Wait for PVC to be bound
echo "⏳ Waiting for PVC to be bound..."
kubectl -n docker-registry wait --for=condition=Bound pvc/docker-registry-storage --timeout=300s

# Wait for deployment to be ready
echo "⏳ Waiting for Docker Registry deployment to be ready..."
kubectl -n docker-registry wait --for=condition=Available deployment/docker-registry --timeout=300s

# Wait for pods to be ready
echo "⏳ Waiting for Docker Registry pods to be ready..."
kubectl -n docker-registry wait --for=condition=Ready pod -l app=docker-registry --timeout=300s

# Check ingress status
echo "🌐 Checking IngressRoute status..."
kubectl -n docker-registry get ingressroute docker-registry-ingressroute

# Display useful information
echo ""
echo "✅ Docker Registry deployment completed successfully!"
echo ""
echo "📋 Deployment Summary:"
echo "====================="
echo "• Namespace: docker-registry"
echo "• Storage: 50GB persistent volume using Ceph (rook-ceph-block)"
echo "• URL: https://docker.alienai.id"
echo "• Registry API: https://docker.alienai.id/v2/"
echo ""
echo "🔧 Useful Commands:"
echo "==================="
echo "• Check pods: kubectl -n docker-registry get pods"
echo "• Check PVC: kubectl -n docker-registry get pvc"
echo "• Check IngressRoute: kubectl -n docker-registry get ingressroute"
echo "• View logs: kubectl -n docker-registry logs -l app=docker-registry"
echo "• Test registry: curl -k https://docker.alienai.id/v2/"
echo ""
echo "🐳 Docker Usage:"
echo "================"
echo "• Tag image: docker tag myimage:latest docker.alienai.id/myimage:latest"
echo "• Push image: docker push docker.alienai.id/myimage:latest"
echo "• Pull image: docker pull docker.alienai.id/myimage:latest"
echo ""
echo "⚠️  Note: SSL certificate may take a few minutes to be issued by Let's Encrypt"
echo "    You can check certificate status with:"
echo "    kubectl -n docker-registry describe certificate docker-registry-tls"
