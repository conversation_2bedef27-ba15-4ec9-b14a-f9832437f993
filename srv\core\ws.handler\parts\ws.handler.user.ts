import { MSG_CTX_USER, type Item } from "cm_net";
import { gatewayStore } from "../../../../instance";
import { WSHandlerPart } from "./ws.handler._part";

export class WSHandlerAuth extends WSHandlerPart {
    public get uid(): string | undefined {
        const uids = this.wsHandler.vars.get("_uids");
        return uids?.[0];
    }
    public set uid(uid: string | undefined) {
        this.wsHandler.vars.set("_uids", uid ? [uid] : []);

        this.sendCurrentUser();
    }

    async sendCurrentUser() {
        const users: Item[] = [];
        const uid = this.uid;

        if (uid) {
            const user = await gatewayStore.userGetById(uid);
            if (user) {
                users.push({
                    id: user.id,
                    name: user.name,
                    pic: user.pic,
                });
            }
        }
        this.wsHandler.send(MSG_CTX_USER, [users]);
    }
}
