import { gatewayStore } from "../../../../../instance";
import { $fn } from "../../../../core/fn";
import { DOOR_PIN } from "./device.state.iot.door";

export interface UserAccess {
    name: string;
    identityType: string;
    userName: string;
}

async function getUserAccess(rfid: string): Promise<UserAccess | undefined> {
    rfid = rfid.trim();

    switch (rfid) {
        case "04 30 0f 8a 92 70 80":
            return {
                name: "KTP Chito",
                identityType: "owner",
                userName: "Chito",
            };

        case "66 16 be f4":
            return {
                name: "KTP Anak Kos 1",
                identityType: "tenant",
                userName: "Anak Kos 1",
            };
    }

    return undefined;
}

$fn("iot.rfid.read", async function (rfid: string) {
    const doorId = "kost_gate";
    l("on rfid read", rfid);

    const identity = await getUserAccess(rfid);
    if (identity) {
        await this.invokePeer(doorId, "btn", [DOOR_PIN, 10000]);

        const deviceName = await gatewayStore.deviceGetName(doorId);

        await gatewayStore.msgPut({
            rid: doorId,
            deviceId: doorId,
            uid: this.user.uid,
            txt: `${identity?.name} ${"membuka"} ${deviceName} dengan ${identity?.identityType} ${identity?.name}`,
        });
    } else {
        // await gatewayStore.msgPut({
        //     rid: doorId,
        //     deviceId: doorId,
        //     uid: this.user.uid,
        //     txt: `Tidak ada akses untuk ${rfid}`,
        // });
    }
});
