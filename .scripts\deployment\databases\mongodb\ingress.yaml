# MongoDB TCP Ingress (for Traefik)
# Note: MongoDB uses TCP protocol, so this requires special Traefik configuration

apiVersion: traefik.containo.us/v1alpha1
kind: IngressRouteTCP
metadata:
  name: mongodb-tcp
  namespace: mongodb
  labels:
    app: mongodb
spec:
  entryPoints:
    - mongodb
  routes:
  - match: HostSNI(`mongodb.alienai.id`)
    services:
    - name: mongodb
      port: 27017
  tls:
    passthrough: false
---
# Alternative: Use regular Ingress for MongoDB Express (Web UI)
# This would require deploying MongoDB Express separately
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mongodb-express
  namespace: mongodb
  labels:
    app: mongodb-express
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - mongodb-express.alienai.id
    secretName: mongodb-express-tls
  rules:
  - host: mongodb-express.alienai.id
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mongodb-express
            port:
              number: 8081
