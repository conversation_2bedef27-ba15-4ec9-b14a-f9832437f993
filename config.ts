export function env(key: string, def: any) {
    return process.env[key] || def;
}

export const PORT = env("PORT", 9494);
export const SERVERS_HOST = "************";
export const OLLAMA_HOST = "************";

// ------------------------------------ Redis
// export const REDIS_URL = env("REDIS_URL", `redis://${SERVERS_HOST}:6379`);

// ------------------------------------ SurrealDB
export const SURREAL_URL = env("SURREAL_URL", `ws://${SERVERS_HOST}:8000/rpc`);
export const SURREAL_USER = env("SURREAL_USER", "root");
export const SURREAL_PASS = env(
  "SURREAL_PASS",
  "PncGD51LsEXBHtdH73FbclMJxcYuAk6Y",
);

// ------------------------------------ Ollama
export const OLLAMA_URL = env("OLLAMA_URL", `http://${OLLAMA_HOST}:10001/`);
