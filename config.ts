export function env(key: string, def: any) {
    return process.env[key] || def;
}

export const PORT = env("PORT", 9494);
export const SERVERS_HOST = "************";
export const OLLAMA_HOST = "************";

// ------------------------------------ Redis
export const REDIS_HOST = env("REDIS_HOST", SERVERS_HOST);
export const REDIS_PORT = env("REDIS_PORT", 6379);
export const REDIS_PASSWORD = env("REDIS_PASSWORD", "Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8");
export const REDIS_URL = env("REDIS_URL", `redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}`);

// ------------------------------------ SurrealDB
export const SURREAL_URL = env("SURREAL_URL", `ws://${SERVERS_HOST}:8000/rpc`);
export const SURREAL_USER = env("SURREAL_USER", "root");
export const SURREAL_PASS = env(
  "SURREAL_PASS",
  "PncGD51LsEXBHtdH73FbclMJxcYuAk6Y",
);

// ------------------------------------ Ollama
export const OLLAMA_URL = env("OLLAMA_URL", `http://${OLLAMA_HOST}:10001/`);
