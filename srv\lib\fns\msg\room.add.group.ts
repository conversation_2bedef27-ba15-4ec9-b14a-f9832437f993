import { $fn } from "../../../core/fn";
import { gatewayStore } from "../../../../instance";
import { nanoid } from "nanoid";
import { $err, type Room } from "cm_net";

$fn(
    "r.add.group",
    async function (name: string) {
        const uid = this.user.uid;
        if (!uid) throw $err.noUser();
        if(typeof name !== 'string') throw $err.err('name-required', `Name must be string`, name);

        const rid = nanoid();

        // create room
        const r : Room = {
            id: rid,
            name: name,
            type: "group",
            owner: uid,
        };
        l('r', r);
        await gatewayStore.roomPut(r);

        // add access
        await gatewayStore.roomUserSet(rid, uid, "owner");

        return rid;
    },
);
