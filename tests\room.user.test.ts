import { gatewayStore } from "../instance";

export async function testRoomUser() {
    l("-------- Test Put msg");
    await gatewayStore.msgPut({
        uid: "chito",
        rid: "chito",
        txt: "<PERSON><PERSON> Chi<PERSON>",
    }, {
        checkAccess: true,
    });

    await gatewayStore.msgPut({
        uid: "chito",
        rid: "chito|este",
        txt: "Ruang Chito & Este",
    }, {
        checkAccess: true,
    });
    l("Done Push Msg");

    l("-------- Test room list");
    const rooms = await gatewayStore.roomList("chito");
    l("rooms", rooms);
}

testRoomUser();
