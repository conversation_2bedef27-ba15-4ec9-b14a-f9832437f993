#!/bin/bash
set -e

# Delete existing TiKV deployment
echo "Deleting existing TiKV deployment..."
kubectl delete statefulset tikv tikv-pd -n surreal --ignore-not-found=true
kubectl delete service tikv tikv-pd -n surreal --ignore-not-found=true

# Deploy Rook-Ceph
echo "Deploying Rook-Ceph CRDs..."
kubectl apply -f .scripts/deployment/infrastructure/ceph/crds.yaml

echo "Deploying Rook-Ceph common resources..."
kubectl apply -f .scripts/deployment/infrastructure/ceph/common.yaml

echo "Deploying Rook-Ceph operator..."
kubectl apply -f .scripts/deployment/infrastructure/ceph/operator.yaml

# Wait for the operator to be ready
echo "Waiting for Rook-Ceph operator to be ready..."
kubectl -n rook-ceph wait --for=condition=Available deployment/rook-ceph-operator --timeout=300s

# Deploy Ceph cluster
echo "Deploying Ceph cluster..."
kubectl apply -f .scripts/deployment/infrastructure/ceph/cluster.yaml

# Wait for the Ceph cluster to be ready (this may take a few minutes)
echo "Waiting for Ceph cluster to be ready..."
sleep 60
kubectl -n rook-ceph get cephclusters

# Create the Ceph storage class
echo "Creating Ceph storage class..."
kubectl apply -f .scripts/deployment/infrastructure/ceph/storageclass.yaml

# Deploy TiKV with Ceph storage
echo "Deploying TiKV with Ceph storage..."
kubectl apply -f .scripts/deployment/storage/tikv/tikv-ceph.yaml

# Wait for TiKV to be ready
echo "Waiting for TiKV to be ready..."
kubectl -n surreal wait --for=condition=Ready pod/tikv-pd-0 --timeout=300s
kubectl -n surreal wait --for=condition=Ready pod/tikv-0 --timeout=300s

# Update SurrealDB to use TiKV
echo "Updating SurrealDB to use TiKV..."
kubectl apply -f .scripts/deployment/surrealdb.yaml

echo "Deployment completed!"
echo "Check the status with: kubectl get pods -n rook-ceph && kubectl get pods -n surreal"
