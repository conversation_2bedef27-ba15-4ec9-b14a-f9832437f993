apiVersion: ceph.rook.io/v1
kind: CephCluster
metadata:
  name: rook-ceph
  namespace: rook-ceph
spec:
  cephVersion:
    image: quay.io/ceph/ceph:v17.2.6
    allowUnsupported: false
  dataDirHostPath: /var/lib/rook
  mon:
    count: 1
    allowMultiplePerNode: true
  dashboard:
    enabled: true
    ssl: false
  monitoring:
    enabled: false
  network:
    hostNetwork: false
  storage:
    useAllNodes: true
    useAllDevices: false
    config:
      osdsPerDevice: "1"
    directories:
    - path: /var/lib/rook/osd
