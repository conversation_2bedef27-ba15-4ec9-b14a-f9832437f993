#!/bin/bash
set -e

# Create the surreal namespace if it doesn't exist
kubectl get namespace surreal || kubectl create namespace surreal

# Deploy Rook-Ceph
echo "Deploying Rook-Ceph operator..."
kubectl apply -f .scripts/deployment/rook-ceph/common.yaml
kubectl apply -f .scripts/deployment/rook-ceph/operator.yaml

# Wait for the operator to be ready
echo "Waiting for Rook-Ceph operator to be ready..."
kubectl -n rook-ceph wait --for=condition=Available deployment/rook-ceph-operator --timeout=300s

# Deploy Ceph cluster
echo "Deploying Ceph cluster..."
kubectl apply -f .scripts/deployment/rook-ceph/cluster.yaml

# Wait for the Ceph cluster to be ready (this may take a few minutes)
echo "Waiting for Ceph cluster to be ready..."
sleep 60
kubectl -n rook-ceph get cephclusters -o jsonpath='{.items[0].status.phase}'

# Create the Ceph storage class
echo "Creating Ceph storage class..."
kubectl apply -f .scripts/deployment/rook-ceph/storageclass.yaml

# Deploy TiKV cluster
echo "Deploying TiKV cluster..."
kubectl apply -f .scripts/deployment/tikv-cluster.yaml

# Wait for TiKV cluster to be ready
echo "Waiting for TiKV cluster to be ready..."
sleep 60
kubectl -n surreal get tidbcluster

# Deploy SurrealDB
echo "Deploying SurrealDB..."
kubectl apply -f .scripts/deployment/surrealdb.yaml

echo "Deployment completed!"
echo "Check the status with: kubectl get pods -n rook-ceph && kubectl get pods -n surreal"
