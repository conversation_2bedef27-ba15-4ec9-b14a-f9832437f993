#!/bin/bash
set -e

echo "🚀 Deploying Ollama with Ceph persistent storage..."

# Create namespace
echo "📁 Creating Ollama namespace..."
kubectl apply -f .scripts/deployment/ai-ml/ollama/ollama-namespace.yaml

# Create PVCs with Ceph storage
echo "💾 Creating persistent volume claims with Ceph storage..."
kubectl apply -f .scripts/deployment/ai-ml/ollama/ollama-pvc.yaml

# Wait for PVCs to be bound
echo "⏳ Waiting for PVCs to be bound..."
kubectl wait --for=condition=Bound pvc/ollama-models -n ollama --timeout=300s
kubectl wait --for=condition=Bound pvc/ollama-data -n ollama --timeout=300s

# Deploy Ollama
echo "🤖 Deploying Ollama..."
kubectl apply -f .scripts/deployment/ollama-deployment.yaml

# Create services
echo "🌐 Creating Ollama services..."
kubectl apply -f .scripts/deployment/ollama-service.yaml

# Wait for deployment to be ready
echo "⏳ Waiting for Ollama deployment to be ready..."
kubectl wait --for=condition=Available deployment/ollama -n ollama --timeout=300s

# Create ingress (optional)
echo "🌍 Creating Ollama ingress..."
kubectl apply -f .scripts/deployment/ollama-ingress.yaml

echo "✅ Ollama deployment completed!"
echo ""
echo "📊 Deployment Status:"
kubectl get pods -n ollama
echo ""
kubectl get pvc -n ollama
echo ""
kubectl get svc -n ollama
echo ""
echo "🔗 Access Information:"
echo "- Internal cluster access: http://ollama.ollama.svc.cluster.local:11434"
echo "- NodePort access: http://<node-ip>:31434"
echo "- Ingress access: https://ollama.alienai.id (if configured)"
echo ""
echo "📝 To download models, exec into the pod and run:"
echo "kubectl exec -it deployment/ollama -n ollama -- ollama pull llama2"
echo "kubectl exec -it deployment/ollama -n ollama -- ollama pull codellama"
echo ""
echo "🧪 Test the API:"
echo "kubectl port-forward -n ollama svc/ollama 11434:11434"
echo "curl http://localhost:11434/api/tags"
