import { gatewayStore } from "../../../../../instance";
import { $fn } from "../../../../core/fn";

$fn("auth.remote", async function (token: string, type: string, name: string) {
    // update remote session
    // TODO: send this
    const peerSid = token.split(":")[0];
    l('auth remote', peerSid, type, name);
    const peer = this.getPeer(peerSid);
    if (!peer) {
        throw new Error("Remote sid not found");
    }

    // set user
    peer.user.uid = this.user.uid;

    // set device name
    await gatewayStore.sessionSet({
        id: peerSid,
        name: name,
    });
});
