# Docker Registry Deployment Script for Kubernetes
# PowerShell version for Windows

Write-Host "🐳 Deploying Docker Registry with Persistent Storage and Traefik Ingress" -ForegroundColor Cyan
Write-Host "=========================================================================" -ForegroundColor Cyan

# Check if kubectl is available
try {
    kubectl version --client --output=json | Out-Null
    Write-Host "✅ kubectl is available" -ForegroundColor Green
} catch {
    Write-Host "❌ kubectl is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check if cluster is accessible
try {
    kubectl cluster-info | Out-Null
    Write-Host "✅ Kubernetes cluster is accessible" -ForegroundColor Green
} catch {
    Write-Host "❌ Cannot connect to Kubernetes cluster" -ForegroundColor Red
    exit 1
}

# Check if cert-manager cluster issuer exists
Write-Host "🔍 Checking if cert-manager cluster issuer exists..." -ForegroundColor Yellow
try {
    kubectl get clusterissuer letsencrypt-prod | Out-Null
    Write-Host "✅ cert-manager cluster issuer found" -ForegroundColor Green
} catch {
    Write-Host "⚠️  letsencrypt-prod cluster issuer not found" -ForegroundColor Yellow
    Write-Host "Deploying cluster issuer..." -ForegroundColor Yellow
    kubectl apply -f .scripts/deployment/cluster-issuer.yaml
}

# Deploy Docker Registry
Write-Host "🚀 Deploying Docker Registry..." -ForegroundColor Yellow
kubectl apply -f .scripts/deployment/docker-registry.yaml

# Wait for namespace to be created
Write-Host "⏳ Waiting for namespace to be ready..." -ForegroundColor Yellow
kubectl wait --for=condition=Active namespace/docker-registry --timeout=60s

# Wait for PVC to be bound
Write-Host "⏳ Waiting for PVC to be bound..." -ForegroundColor Yellow
kubectl -n docker-registry wait --for=condition=Bound pvc/docker-registry-storage --timeout=300s

# Wait for deployment to be ready
Write-Host "⏳ Waiting for Docker Registry deployment to be ready..." -ForegroundColor Yellow
kubectl -n docker-registry wait --for=condition=Available deployment/docker-registry --timeout=300s

# Wait for pods to be ready
Write-Host "⏳ Waiting for Docker Registry pods to be ready..." -ForegroundColor Yellow
kubectl -n docker-registry wait --for=condition=Ready pod -l app=docker-registry --timeout=300s

# Check ingress status
Write-Host "🌐 Checking IngressRoute status..." -ForegroundColor Yellow
kubectl -n docker-registry get ingressroute docker-registry-ingressroute

# Display useful information
Write-Host ""
Write-Host "✅ Docker Registry deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Deployment Summary:" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan
Write-Host "• Namespace: docker-registry" -ForegroundColor White
Write-Host "• Storage: 50GB persistent volume using local-path storage" -ForegroundColor White
Write-Host "• URL: https://docker.alienai.id" -ForegroundColor White
Write-Host "• Registry API: https://docker.alienai.id/v2/" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Useful Commands:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan
Write-Host "• Check pods: kubectl -n docker-registry get pods" -ForegroundColor White
Write-Host "• Check PVC: kubectl -n docker-registry get pvc" -ForegroundColor White
Write-Host "• Check IngressRoute: kubectl -n docker-registry get ingressroute" -ForegroundColor White
Write-Host "• View logs: kubectl -n docker-registry logs -l app=docker-registry" -ForegroundColor White
Write-Host "• Test registry: curl https://docker.alienai.id/v2/" -ForegroundColor White
Write-Host ""
Write-Host "🐳 Docker Usage:" -ForegroundColor Cyan
Write-Host "================" -ForegroundColor Cyan
Write-Host "• Tag image: docker tag myimage:latest docker.alienai.id/myimage:latest" -ForegroundColor White
Write-Host "• Push image: docker push docker.alienai.id/myimage:latest" -ForegroundColor White
Write-Host "• Pull image: docker pull docker.alienai.id/myimage:latest" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Note: SSL certificate may take a few minutes to be issued by Let's Encrypt" -ForegroundColor Yellow
Write-Host "    You can check certificate status with:" -ForegroundColor Yellow
Write-Host "    kubectl -n docker-registry describe certificate docker-registry-tls" -ForegroundColor Yellow
