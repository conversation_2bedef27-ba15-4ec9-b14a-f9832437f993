# Redis Credentials Reference
# DO NOT COMMIT THIS FILE TO VERSION CONTROL

redis_credentials:
  admin:
    password: "Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8"
    base64: "UmQ1S3g5UHEyTnY3V3I1WnQzQm02THQ0SGoxUXc4"
  
  app:
    password: "Ap7Mx4Kp9Nv2Wr8Zt6Bm3Lp1Hj5Qw9"
    base64: "QXA3TXg0S3A5TnYyV3I4WnQ2Qm0zTHAxSGo1UXc5"

connection_strings:
  internal_cluster:
    admin: "redis://:<EMAIL>:6379"
    app: "redis://:<EMAIL>:6379"
  
  nodeport:
    admin: "redis://:Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8@<node-ip>:32379"
    app: "redis://:Ap7Mx4Kp9Nv2Wr8Zt6Bm3Lp1Hj5Qw9@<node-ip>:32379"

test_commands:
  connect: "kubectl exec -it deployment/redis -n redis -- redis-cli -a Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8"
  info: "kubectl exec -it deployment/redis -n redis -- redis-cli -a Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8 info"
  ping: "kubectl exec -it deployment/redis -n redis -- redis-cli -a Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8 ping"
