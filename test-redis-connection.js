const net = require('net');

const REDIS_HOST = '************';
const REDIS_PORT = 6379;
const REDIS_PASSWORD = 'Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8';

console.log(`Testing Redis connection to ${REDIS_HOST}:${REDIS_PORT}...`);

const client = new net.Socket();

client.setTimeout(5000);

client.on('connect', () => {
    console.log('✅ Connected to Redis server');
    
    // Send AUTH command
    client.write(`AUTH ${REDIS_PASSWORD}\r\n`);
    
    // Send PING command
    setTimeout(() => {
        client.write('PING\r\n');
    }, 100);
    
    // Close connection after test
    setTimeout(() => {
        client.write('QUIT\r\n');
    }, 200);
});

client.on('data', (data) => {
    const response = data.toString();
    console.log('📨 Redis response:', response.trim());
});

client.on('timeout', () => {
    console.log('❌ Connection timeout');
    client.destroy();
});

client.on('error', (err) => {
    console.log('❌ Connection error:', err.message);
});

client.on('close', () => {
    console.log('🔌 Connection closed');
});

client.connect(REDIS_PORT, REDIS_HOST);
