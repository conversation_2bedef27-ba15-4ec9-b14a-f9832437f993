apiVersion: v1
kind: Service
metadata:
  name: ollama
  namespace: ollama
  labels:
    app: ollama
spec:
  type: ClusterIP
  ports:
  - port: 11434
    targetPort: 11434
    protocol: TCP
    name: http
  selector:
    app: ollama
---
apiVersion: v1
kind: Service
metadata:
  name: ollama-nodeport
  namespace: ollama
  labels:
    app: ollama
spec:
  type: NodePort
  ports:
  - port: 11434
    targetPort: 11434
    protocol: TCP
    name: http
    nodePort: 31434  # External access via node IP:31434
  selector:
    app: ollama
