import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./ws.handler/ws.handler";

export type Fn = (this: WSHandler, ...args: any[]) => Promise<any>;
const fns: { [id: string]: Fn } = {};

export function $fn(id: string, fn: Fn) {
    fns[id] = fn;
}

export async function $fnRun(ws: WSHandler, id: string, args: any[]=[]) {
    const fn = fns[id];
    if (!fn) throw new Error(`Function '${id}' not found`);

    const result = await fn.apply(ws, args);
    return result;
}
