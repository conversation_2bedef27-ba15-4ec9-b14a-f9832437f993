apiVersion: v1
kind: Namespace
metadata:
  name: cm-gateway
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cm-gateway
  namespace: cm-gateway
  labels:
    app: cm-gateway
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cm-gateway
  template:
    metadata:
      labels:
        app: cm-gateway
    spec:
      volumes:
        - name: cert-storage
          persistentVolumeClaim:
            claimName: cert-storage
      containers:
        - name: cm-gateway
          image: docker.alienai.id/chitosa/cm-gateway:latest
          ports:
            - containerPort: 9494
          volumeMounts:
            - name: cert-storage
              mountPath: /certs
          env:
            # - name: OLLAMA_URL
            #   value: "http://100.100.11.1:10001"
            # - name: REDIS_URL
            #   value: "redis://100.100.11.1:6379"
            # - name: MQTT_BROKER
            #   value: "ws://100.100.11.1/mqtt"
            # - name: SURREAL_URL
            #   value: "ws://100.100.11.1:8000/rpc"
            # - name: SURREAL_USER
            #   value: "root"
            # - name: SURREAL_PASS
            #   value: "PncGD51LsEXBHtdH73FbclMJxcYuAk6Y"

          resources:
            limits:
              cpu: "1"
              memory: "512Mi"
            requests:
              cpu: "250m"
              memory: "128Mi"
---
apiVersion: v1
kind: Service
metadata:
  name: cm-gateway
  namespace: cm-gateway
spec:
  selector:
    app: cm-gateway
  ports:
    - protocol: TCP
      port: 9494
      targetPort: 9494
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cm-gateway-ingress
  namespace: cm-gateway
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    traefik.ingress.kubernetes.io/router.entrypoints: "websecure"
    traefik.ingress.kubernetes.io/router.tls: "true"
spec:
  ingressClassName: traefik
  tls:
  - hosts:
    - "warp.alienai.id"
    secretName: cm-gateway-tls
  rules:
    - host: "warp.alienai.id"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: cm-gateway
                port:
                  number: 9494
