#!/bin/bash
set -e

echo "🚀 Deploying Redis with Ceph persistent storage..."

# Create namespace
echo "📁 Creating Redis namespace..."
kubectl apply -f .scripts/deployment/databases/redis/namespace.yaml

# Create secrets
echo "🔐 Creating Redis secrets..."
kubectl apply -f .scripts/deployment/databases/redis/secret.yaml

# Create ConfigMap
echo "⚙️ Creating Redis configuration..."
kubectl apply -f .scripts/deployment/databases/redis/configmap.yaml

# Create PVCs with Ceph storage
echo "💾 Creating persistent volume claims with Ceph storage..."
kubectl apply -f .scripts/deployment/databases/redis/pvc.yaml

# Wait for PVCs to be bound
echo "⏳ Waiting for PVCs to be bound..."
kubectl wait --for=condition=Bound pvc/redis-data -n redis --timeout=300s
kubectl wait --for=condition=Bound pvc/redis-config -n redis --timeout=300s

# Deploy Redis
echo "🗄️ Deploying Redis..."
kubectl apply -f .scripts/deployment/databases/redis/deployment.yaml

# Create services
echo "🌐 Creating Redis services..."
kubectl apply -f .scripts/deployment/databases/redis/service.yaml

# Wait for deployment to be ready
echo "⏳ Waiting for Redis deployment to be ready..."
kubectl wait --for=condition=Available deployment/redis -n redis --timeout=300s

echo "✅ Redis deployment completed!"
echo ""
echo "📊 Deployment Status:"
kubectl get pods -n redis
echo ""
kubectl get pvc -n redis
echo ""
kubectl get svc -n redis
echo ""
echo "🔗 Access Information:"
echo "- Internal cluster access: redis://redis.redis.svc.cluster.local:6379"
echo "- NodePort access: redis://<node-ip>:32379"
echo ""
echo "🔐 Credentials:"
echo "- Redis password: Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8"
echo "- App Redis password: Ap7Mx4Kp9Nv2Wr8Zt6Bm3Lp1Hj5Qw9"
echo ""
echo "🧪 Test the connection:"
echo "kubectl exec -it deployment/redis -n redis -- redis-cli -a Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8"
echo ""
echo "📝 Connection strings:"
echo "- Admin: redis://:<EMAIL>:6379"
echo "- App: redis://:<EMAIL>:6379"
echo ""
echo "🔧 Redis CLI commands:"
echo "- Connect: kubectl exec -it deployment/redis -n redis -- redis-cli -a <password>"
echo "- Info: kubectl exec -it deployment/redis -n redis -- redis-cli -a <password> info"
echo "- Monitor: kubectl exec -it deployment/redis -n redis -- redis-cli -a <password> monitor"
