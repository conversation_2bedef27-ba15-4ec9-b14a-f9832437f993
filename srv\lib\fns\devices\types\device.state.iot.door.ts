import type { IOTDoorState } from "cm_net";
import { gatewayStore } from "../../../../../instance";
import { $fn } from "../../../../core/fn";

// ------------------------ gate
export const DOOR_PIN = 5;
$fn("iot.door.state.set", async function (doorId: string, state: IOTDoorState) {
    // store device state
    await gatewayStore.sessionSet({
        id: doorId,
        state: state,
    });

    // send command
    switch (state) {
        case "open":
            await this.invokePeer(doorId, "btn", [DOOR_PIN, 10000]);
            break;

        case "lock-open":
            await this.invokePeer(doorId, "sw", [DOOR_PIN, true]);
            break;
        case "lock-close":
            await this.invokePeer(doorId, "sw", [DOOR_PIN, false]);
            break;
    }

    // put log
    const userName = await gatewayStore.userGetName(this.user.uid);
    const deviceName = await gatewayStore.deviceGetName(doorId);

    const stateName = {
        "lock-open": "mengunci buka",
        "lock-close": "mengunci tutup",
        open: "membuka",
    }[state];

    await gatewayStore.msgPut({
        rid: doorId,
        deviceId: doorId,
        uid: this.user.uid,
        txt: `${userName} ${stateName} ${deviceName}`,
    });
});
