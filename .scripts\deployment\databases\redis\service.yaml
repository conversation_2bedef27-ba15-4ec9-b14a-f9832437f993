apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: redis
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis
---
apiVersion: v1
kind: Service
metadata:
  name: redis-nodeport
  namespace: redis
  labels:
    app: redis
spec:
  type: NodePort
  ports:
  - port: 6379
    targetPort: 6379
    nodePort: 32379
    protocol: TCP
    name: redis
  selector:
    app: redis
