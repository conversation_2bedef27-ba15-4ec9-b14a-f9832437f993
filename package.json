{"name": "cm_gateway", "module": "server.ts", "types": "server.ts", "type": "module", "private": true, "scripts": {"publish": "bun .scripts/publish.ts", "build": "bun build server.ts --target=bun --outfile=build/bundle.js"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"cm_net": "file:../cm_net", "cm_sdb": "file:../cm_sdb", "commander": "^13.1.0", "dayjs": "^1.11.13", "firebase-admin": "^13.3.0", "google-auth-library": "^9.15.1", "ioredis": "^5.6.1", "msgpackr": "^1.11.2", "nanoid": "^5.1.5", "surrealdb": "^1.3.2"}, "c": {"index": ["srv/lib"]}}