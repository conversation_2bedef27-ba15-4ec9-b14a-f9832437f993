# Docker Registry Deployment

This directory contains the configuration and scripts to deploy a Docker registry on Kubernetes with persistent storage and Traefik ingress.

## 🎯 Overview

The Docker registry is deployed with:
- **Persistent Storage**: 50GB volume using `local-path` storage class
- **SSL/TLS**: Automatic certificate management via cert-manager and Let's Encrypt
- **Ingress**: Traefik IngressRoute for HTTP and HTTPS access
- **Domain**: `docker.alienai.id`

## 📁 Files

- `docker-registry.yaml` - Complete Kubernetes deployment manifest
- `deploy-docker-registry.sh` - Bash deployment script (Linux/macOS)
- `deploy-docker-registry.ps1` - PowerShell deployment script (Windows)
- `README-docker-registry.md` - This documentation

## 🚀 Deployment

### Prerequisites
- Kubernetes cluster with kubectl access
- Traefik ingress controller installed
- cert-manager installed with Let's Encrypt cluster issuer

### Quick Deploy
```bash
# Linux/macOS
./deploy-docker-registry.sh

# Windows PowerShell
.\deploy-docker-registry.ps1

# Manual deployment
kubectl apply -f docker-registry.yaml
```

## 🔧 Configuration Details

### Storage
- **Storage Class**: `local-path` (fallback from Ceph due to operator issues)
- **Size**: 50GB
- **Access Mode**: ReadWriteOnce

### Networking
- **HTTP**: `http://docker.alienai.id`
- **HTTPS**: `https://docker.alienai.id`
- **Registry API**: `https://docker.alienai.id/v2/`

### Security
- **TLS**: Automatic SSL certificate from Let's Encrypt
- **Authentication**: None (consider adding for production)

## 🐳 Usage Examples

### Tag and Push
```bash
# Tag your image
docker tag myimage:latest docker.alienai.id/myimage:latest

# Push to registry
docker push docker.alienai.id/myimage:latest
```

### Pull
```bash
# Pull from registry
docker pull docker.alienai.id/myimage:latest
```

### List Repositories
```bash
curl https://docker.alienai.id/v2/_catalog
```

### List Tags
```bash
curl https://docker.alienai.id/v2/myimage/tags/list
```

## 🔍 Monitoring

### Check Status
```bash
# Check all resources
kubectl -n docker-registry get all

# Check pods
kubectl -n docker-registry get pods

# Check persistent volume
kubectl -n docker-registry get pvc

# Check IngressRoute
kubectl -n docker-registry get ingressroute

# Check certificate
kubectl -n docker-registry get certificate
```

### View Logs
```bash
kubectl -n docker-registry logs -l app=docker-registry
```

### Test Registry
```bash
# Test API endpoint
curl https://docker.alienai.id/v2/

# List repositories
curl https://docker.alienai.id/v2/_catalog
```

## 🛠️ Troubleshooting

### Common Issues

1. **404 Errors**: Ensure IngressRoute is properly configured and Traefik is running
2. **Certificate Issues**: Check cert-manager logs and certificate status
3. **Storage Issues**: Verify storage class exists and PVC is bound
4. **Push/Pull Failures**: Check Docker daemon configuration for insecure registries if needed

### Useful Commands
```bash
# Check Traefik logs
kubectl -n kube-system logs -l app.kubernetes.io/name=traefik

# Check cert-manager logs
kubectl -n cert-manager logs -l app=cert-manager

# Describe certificate for troubleshooting
kubectl -n docker-registry describe certificate docker-registry-tls

# Port forward for local testing
kubectl -n docker-registry port-forward service/docker-registry 5000:5000
```

## 📝 Notes

- The registry is currently configured without authentication
- For production use, consider adding basic authentication or OAuth
- The storage uses `local-path` instead of Ceph due to operator permission issues
- SSL certificate may take a few minutes to be issued initially

## ✅ Verification

The deployment is successful when:
1. Pod is running: `kubectl -n docker-registry get pods`
2. PVC is bound: `kubectl -n docker-registry get pvc`
3. Certificate is ready: `kubectl -n docker-registry get certificate`
4. API responds: `curl https://docker.alienai.id/v2/`
5. Push/pull operations work successfully
