import type { ServerWebSocket } from "bun";
import type { ServerWSData, W<PERSON><PERSON><PERSON><PERSON> } from "./ws.handler";
import { HGeneric } from "../../lib/handlers/h.generic";


type WSHandlerBuilder = (
    ws: ServerWebSocket<ServerWSData>,
) => WSHandler;

const handlerBuilders: { [type: string]: WSHandlerBuilder } = {};
export function $wsHandler(type: string, builder: WSHandlerBuilder) {
    handlerBuilders[type] = builder;
}

export function $buildWSHandler(ws: ServerWebSocket<ServerWSData>) {
    return new HGeneric(ws);

    // const handlerBuilder = handlerBuilders[ws.data.handlerType];
    // if (handlerBuilder) { return handlerBuilder(ws); }

    // console.log("DANGER", `No handler for ${ws.data.handlerType}`);
}

