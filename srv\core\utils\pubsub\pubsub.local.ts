// deno-lint-ignore-file no-explicit-any require-await
import {
  PubSubBackend,
  type SubscribtionCallback,
  type SubscribtionCancel,
} from "./pubsub.ts";

export class PubSubLocal extends PubSubBackend {
  async connect(): Promise<any> {
    // throw new Error("Method not implemented.");
  }
  async disconnect(): Promise<any> {
    // throw new Error("Method not implemented.");
  }

  readonly subscriptions: { [topic: string]: Set<SubscribtionCallback> } = {};
  subscribe(topic: string, cb: SubscribtionCallback<any>): SubscribtionCancel {
    const subs = this.subscriptions[topic] ??= new Set();
    subs.add(cb);
    return () => {
      subs.delete(cb);

      if (subs.size === 0) delete this.subscriptions[topic];
    };
  }
  async publish(topic: string, data: any): Promise<any> {
    this.subscriptions[topic]?.forEach((l) => {
      l(data);
    });
  }
}
