# Mailu Helm Chart Values Configuration
# Configured for Ceph storage and Kubernetes deployment

# Global settings
global:
  # Domain configuration - CHANGE THIS TO YOUR DOMAIN
  domain: "mail.alienai.id"
  hostnames: 
    - "mail.alienai.id"
  
  # Secret key for Mailu - CH<PERSON><PERSON> THIS TO A RANDOM STRING
  secretKey: "CHANGE-ME-TO-RANDOM-SECRET-KEY-32-CHARS"
  
  # Database configuration
  database:
    type: "postgresql"
    # Will use internal PostgreSQL by default

# Persistence configuration using Ceph storage
persistence:
  # Main mail data storage
  size: "50Gi"
  storageClass: "rook-ceph-block"
  accessMode: "ReadWriteOnce"
  
  # Additional storage for attachments and large files
  attachments:
    size: "100Gi"
    storageClass: "rook-ceph-block"
    accessMode: "ReadWriteOnce"

# Front-end (nginx) configuration
front:
  # Enable the front-end service
  enabled: true
  
  # Service configuration
  service:
    type: ClusterIP
    port: 80
    
  # Resource limits
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "500m"

# Admin interface configuration
admin:
  enabled: true
  
  # Initial admin account - CHANGE THESE CREDENTIALS
  initialAccount:
    username: "admin"
    domain: "mail.alienai.id"
    password: "CHANGE-ME-ADMIN-PASSWORD"
    
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "200m"

# IMAP server configuration
imap:
  enabled: true
  
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"

# SMTP server configuration
smtp:
  enabled: true
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "300m"

# Antispam (Rspamd) configuration
antispam:
  enabled: true
  
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"

# Antivirus (ClamAV) configuration
antivirus:
  enabled: true
  
  resources:
    requests:
      memory: "1Gi"
      cpu: "200m"
    limits:
      memory: "2Gi"
      cpu: "500m"

# Webmail (Roundcube) configuration
webmail:
  enabled: true
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "300m"

# Database configuration (PostgreSQL)
postgresql:
  enabled: true
  
  # Use Ceph storage for database
  persistence:
    enabled: true
    size: "20Gi"
    storageClass: "rook-ceph-block"
    
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"

# Redis configuration
redis:
  enabled: true
  
  # Use Ceph storage for Redis persistence
  persistence:
    enabled: true
    size: "5Gi"
    storageClass: "rook-ceph-block"
    
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "200m"

# Ingress configuration
ingress:
  enabled: true
  className: "traefik"
  
  annotations:
    # Enable SSL certificate generation
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    # Traefik specific annotations
    traefik.ingress.kubernetes.io/router.entrypoints: "websecure"
    traefik.ingress.kubernetes.io/router.tls: "true"
    
  hosts:
    - host: "mail.alienai.id"
      paths:
        - path: "/"
          pathType: "Prefix"
          
  tls:
    - secretName: "mailu-tls-cert"
      hosts:
        - "mail.alienai.id"

# Security settings
security:
  # Enable security headers
  securityHeaders: true
  
  # Pod security context
  podSecurityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
    
  # Container security context
  securityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: false
    capabilities:
      drop:
        - ALL

# Network policies (optional, for enhanced security)
networkPolicy:
  enabled: false

# Monitoring and logging
monitoring:
  enabled: false
  
logging:
  level: "INFO"
