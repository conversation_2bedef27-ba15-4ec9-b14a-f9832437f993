# Kubernetes Deployment Scripts

This directory contains organized deployment scripts and configurations for various services on Kubernetes with Ceph storage.

## 📁 Directory Structure

```
.scripts/deployment/
├── README.md                    # This file
├── infrastructure/              # Core infrastructure components
│   ├── ceph/                   # Ceph storage cluster
│   ├── cert-manager/           # Certificate management
│   └── ingress/                # Ingress controllers
├── databases/                  # Database services
│   ├── mongodb/                # MongoDB deployment
│   ├── postgresql/             # PostgreSQL deployment
│   └── surrealdb/              # SurrealDB deployment
├── storage/                    # Storage and data services
│   ├── tikv/                   # TiKV distributed storage
│   └── registry/               # Docker registry
├── ai-ml/                      # AI/ML services
│   └── ollama/                 # Ollama LLM service
├── communication/              # Communication services
│   └── mailu/                  # Mail server
└── monitoring/                 # Monitoring and observability
    └── prometheus/             # Prometheus stack
```

## 🚀 Quick Start

### Prerequisites

1. **Kubernetes cluster** (1.20+)
2. **kubectl** configured
3. **Helm 3.x** installed
4. **Storage** available (local-path or Ceph)

### Core Infrastructure Deployment Order

1. **Storage**: Deploy Ceph first for persistent storage
2. **Certificates**: Deploy cert-manager for SSL/TLS
3. **Ingress**: Configure Traefik or nginx-ingress
4. **Services**: Deploy individual services as needed

### Deploy Ceph Storage (Required First)

```bash
# Deploy Rook-Ceph operator and cluster
.scripts/deployment/infrastructure/ceph/deploy-ceph.sh
```

### Deploy Individual Services

Each service directory contains:
- `deploy-*.sh` - Deployment script
- `*.yaml` - Kubernetes manifests
- `values.yaml` - Helm values (if applicable)
- `README.md` - Service-specific documentation

## 📊 Service Overview

### Infrastructure Services

| Service | Description | Storage | Status |
|---------|-------------|---------|--------|
| **Ceph** | Distributed storage cluster | Host storage | ✅ Ready |
| **cert-manager** | SSL certificate management | - | ✅ Ready |
| **Traefik** | Ingress controller | - | ✅ Ready |

### Database Services

| Service | Description | Storage | Status |
|---------|-------------|---------|--------|
| **MongoDB** | Document database | Ceph RBD | ✅ Ready |
| **PostgreSQL** | Relational database | Ceph RBD | 🔄 Planned |
| **SurrealDB** | Multi-model database | TiKV | ✅ Ready |

### Storage Services

| Service | Description | Storage | Status |
|---------|-------------|---------|--------|
| **TiKV** | Distributed key-value store | Ceph RBD | ✅ Ready |
| **Docker Registry** | Container image registry | Ceph RBD | ✅ Ready |

### AI/ML Services

| Service | Description | Storage | Status |
|---------|-------------|---------|--------|
| **Ollama** | Local LLM inference | Ceph RBD | ✅ Ready |

### Communication Services

| Service | Description | Storage | Status |
|---------|-------------|---------|--------|
| **Mailu** | Mail server suite | Ceph RBD | ✅ Ready |

## 🔧 Configuration

### Storage Classes

The deployment uses these storage classes:

```yaml
# Ceph block storage (recommended)
storageClassName: "rook-ceph-block"

# Local path storage (development)
storageClassName: "local-path"
```

### Common Environment Variables

Set these environment variables for consistent deployments:

```bash
export CLUSTER_DOMAIN="alienai.id"
export STORAGE_CLASS="rook-ceph-block"
export CERT_ISSUER="letsencrypt-prod"
export INGRESS_CLASS="traefik"
```

## 🛠️ Management Commands

### Check All Deployments

```bash
# Check all namespaces
kubectl get namespaces

# Check all persistent volumes
kubectl get pv

# Check all storage classes
kubectl get storageclass

# Check all ingress routes
kubectl get ingress -A
```

### Resource Monitoring

```bash
# Check node resources
kubectl top nodes

# Check pod resources
kubectl top pods -A

# Check storage usage
kubectl get pvc -A
```

## 🔄 Migration Guide

### From Old Structure

The old flat structure has been reorganized:

```bash
# Old locations → New locations
mongodb/           → databases/mongodb/
ollama/            → ai-ml/ollama/
rook-ceph/         → infrastructure/ceph/
docker-registry.*  → storage/registry/
```

### Update Scripts

Update any existing scripts that reference the old paths:

```bash
# Old
.scripts/deployment/deploy-mongodb.sh

# New
.scripts/deployment/databases/mongodb/deploy-mongodb.sh
```

## 🐛 Troubleshooting

### Common Issues

1. **Storage not available**: Ensure Ceph is deployed and healthy
2. **Ingress not working**: Check Traefik configuration
3. **Certificates not issued**: Verify cert-manager setup
4. **Pods pending**: Check resource availability and storage

### Debug Commands

```bash
# Check Ceph cluster health
kubectl -n rook-ceph get cephclusters

# Check certificate issuers
kubectl get clusterissuer

# Check ingress controller
kubectl -n kube-system get pods -l app=traefik

# Check storage provisioner
kubectl -n rook-ceph get pods -l app=rook-ceph-operator
```

## 📚 Documentation

Each service directory contains detailed documentation:

- **README.md**: Service overview and quick start
- **CONFIGURATION.md**: Detailed configuration options
- **TROUBLESHOOTING.md**: Common issues and solutions

## 🔐 Security

### Best Practices

1. **Use strong passwords** for all services
2. **Enable TLS** for all external connections
3. **Configure RBAC** for service accounts
4. **Regular updates** of container images
5. **Monitor logs** for security events

### Security Scanning

```bash
# Scan for vulnerabilities
kubectl get vulnerabilityreports -A

# Check security policies
kubectl get networkpolicies -A

# Audit RBAC permissions
kubectl auth can-i --list --as=system:serviceaccount:default:default
```

## 🤝 Contributing

When adding new services:

1. Create appropriate directory structure
2. Include deployment scripts and manifests
3. Add comprehensive documentation
4. Test with Ceph storage
5. Update this README

## 📞 Support

For issues and questions:

1. Check service-specific README files
2. Review troubleshooting guides
3. Check Kubernetes and service logs
4. Consult upstream documentation
