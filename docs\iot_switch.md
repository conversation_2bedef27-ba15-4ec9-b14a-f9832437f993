Commands:

- sw(pin, state) pin: pin to control state: 0 = 0ff, 1 = On
- btn(pin, state) pin: pin to control, press for 1 sec, then release

Last state will be stored on this gateway, Will be restored after power failure
(configurable)

Tested Switch Pins:

- ESP8266 Safe Pins: 
  - D1 - GP<PERSON> 5
  - D2 - GP<PERSON> 4
  - D5 - <PERSON><PERSON> 14
  - D6 - GP<PERSON> 12
  - D7 - GP<PERSON> 13
- ESP01 Relay Module Pin: 0

Gate Control:

- D1 (GPIO 5): CLOSE
- D2 (GPIO 4): OPEN
- D5 (GPIO 14): STOP
- D6 (GPIO 12): PEDESTRIAN (Optional)

# Implementation

For CT-GT1100, CT-GT1530, CT-GT2100

Step Down 12v to 5v Module

## Gate Control board to Step Down Module

1. Connect Gate Control Board PIN 7 (12VDC) to Stepdown VIN
2. Connect Gate Control Board PIN 9 (GND) to Stepdown GND

## Step Down module to ESP8266

1. Connect Stepdown VOUT to ESP8266 VIN
2. Connect Stepdown GND to ESP8266 GND

## Gate Control board to ESP8266

1. Connect Gate Control Board PIN 1(Gate Close Control) to ESP8266 PIN D1 (GPIO 5)
2. Connect Gate Control Board PIN 2(Gate Open Control) to ESP8266 PIN D2 (GPIO 4) 
3. Connect Gate Control Board PIN 3(Gate Stop Control) to ESP8266 PIN D5 (GPIO 14)
4. Connect Gate Control Board PIN 4(Ground) to Any ESP8266 Ground Pin 
5. Connect Gate Control Board PIN 6(Gate Open Pedestrian) to ESP8266 PIN D6 (GPIO 12) (Optional)
