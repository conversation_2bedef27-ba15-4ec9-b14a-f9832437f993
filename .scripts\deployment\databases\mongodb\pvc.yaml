apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongodb-data
  namespace: mongodb
  labels:
    app: mongodb
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: local-path
  resources:
    requests:
      storage: 50Gi  # Adjust size based on your needs
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongodb-config
  namespace: mongodb
  labels:
    app: mongodb
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: local-path
  resources:
    requests:
      storage: 1Gi  # For configuration files
