apiVersion: v1
kind: Service
metadata:
  name: tikv-pd
  namespace: surreal
spec:
  ports:
  - name: client
    port: 2379
    protocol: TCP
    targetPort: 2379
  selector:
    app: tikv-pd
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: tikv
  namespace: surreal
spec:
  ports:
  - name: client
    port: 20160
    protocol: TCP
    targetPort: 20160
  selector:
    app: tikv
  type: ClusterIP
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: tikv-pd
  namespace: surreal
spec:
  serviceName: tikv-pd
  replicas: 1
  selector:
    matchLabels:
      app: tikv-pd
  template:
    metadata:
      labels:
        app: tikv-pd
    spec:
      containers:
      - name: pd
        image: pingcap/pd:v6.5.0
        ports:
        - containerPort: 2379
          name: client
        - containerPort: 2380
          name: peer
        volumeMounts:
        - name: pd-data
          mountPath: /var/lib/pd
        env:
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        command:
        - /pd-server
        - --name=$(POD_NAME)
        - --client-urls=http://0.0.0.0:2379
        - --peer-urls=http://0.0.0.0:2380
        - --advertise-client-urls=http://$(POD_IP):2379
        - --advertise-peer-urls=http://$(POD_IP):2380
        - --data-dir=/var/lib/pd
        - --initial-cluster=$(POD_NAME)=http://$(POD_IP):2380
  volumeClaimTemplates:
  - metadata:
      name: pd-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: rook-ceph-block
      resources:
        requests:
          storage: 10Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: tikv
  namespace: surreal
spec:
  serviceName: tikv
  replicas: 3
  selector:
    matchLabels:
      app: tikv
  template:
    metadata:
      labels:
        app: tikv
    spec:
      containers:
      - name: tikv
        image: pingcap/tikv:v6.5.0
        ports:
        - containerPort: 20160
          name: client
        volumeMounts:
        - name: tikv-data
          mountPath: /var/lib/tikv
        env:
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        command:
        - /tikv-server
        - --addr=0.0.0.0:20160
        - --advertise-addr=$(POD_IP):20160
        - --data-dir=/var/lib/tikv
        - --pd=tikv-pd:2379
  volumeClaimTemplates:
  - metadata:
      name: tikv-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: rook-ceph-block
      resources:
        requests:
          storage: 30Gi
