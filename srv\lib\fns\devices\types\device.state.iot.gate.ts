import type { IOTGateState } from "cm_net";
import { $fn } from "../../../../core/fn";
import { gatewayStore } from "../../../../../instance";

// ------------------------ gate
const GATE_PINS: Record<IOTGateState, number> = {
    close: 4, // D2 (GPIO 4)
    open: 5, // D1 (GPIO 5)
    stop: 14, // D5 (GPIO 14)
    small: 12, // D6 (GPIO 12)
};

$fn("iot.gate.state.set", async function (gateId: string, state: IOTGateState) {
    // store device state
    await gatewayStore.sessionSet({
        id: gateId,
        state: state,
    });

    const pin = GATE_PINS[state];

    // send command
    await this.invokePeer(gateId, "btn", [pin, state]);

    // put log
    const userName = await gatewayStore.userGetName(this.user.uid);
    const deviceName = await gatewayStore.deviceGetName(gateId);

    const states : Record<IOTGateState, string> = {
        close: "menutup",
        open: "membuka",
        stop: "menghentikan",
        small: "membuka kecil",
    };

    const stateName = states[state];

    await gatewayStore.msgPut({
        rid: gateId,
        deviceId: gateId,
        uid: this.user.uid,
        txt: `${userName} ${stateName} ${deviceName}`,
    });
});

$fn("iot.gate.state.get", async function (gateId: string) {
    const session = await gatewayStore.sessionGet(gateId);
    return session?.state as IOTGateState;
});
