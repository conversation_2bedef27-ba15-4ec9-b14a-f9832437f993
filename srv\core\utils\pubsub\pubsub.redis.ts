// deno-lint-ignore-file no-explicit-any
import { Redis } from "ioredis";
import { pack, unpack } from "msgpackr";
import { PubSubBackend, type SubscribtionCallback, type SubscribtionCancel } from "./pubsub";

export class PubSubRedis extends PubSubBackend {
  private pub: Redis;
  private sub: Redis;

  constructor(url: string) {
    super();
    this.pub = new Redis(url, { lazyConnect: true });
    this.sub = new Redis(url, { lazyConnect: true });
  }

  async connect(): Promise<any> {
    // console.log(`connecting to redis`);
    await Promise.all([
      await this.pub.connect(),
      await this.sub.connect(),
    ]);

    this.sub.on("messageBuffer", (channel, message) => {
      const topic = channel.toString("utf8");
      // const topic = unpack(channel);
      const val = unpack(message);

      this.subscriptions[topic]?.forEach((sub) => {
        sub(val);
      });
    });
  }
  async disconnect(): Promise<any> {}

  readonly subscriptions: { [topic: string]: Set<SubscribtionCallback> } = {};
  subscribe(topic: string, cb: SubscribtionCallback<any>): SubscribtionCancel {
    const subs = this.subscriptions[topic] ??= new Set();
    subs.add(cb);

    this.sub?.subscribe(topic);

    return () => {
      // l('unsub', topic)
      if (subs.size === 0) {
        this.sub.unsubscribe(topic);
      }
    };
  }

  subscribeOnce(topic: string): Promise<any> {
    return new Promise((resolve) => {
      this.subscribe(topic, (val) => {
        resolve(val);
      });
    });
  }

  async publish(topic: string, data: any): Promise<any> {
    await this.pub.publish(topic, pack(data));
  }
}
