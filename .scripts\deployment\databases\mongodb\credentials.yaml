# MongoDB Credentials Configuration
# Generated on: 2025-05-29

# Root Admin Credentials
MONGODB_ROOT_USERNAME: admin
MONGODB_ROOT_PASSWORD: Mg8Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8

# Application Database Credentials  
MONGODB_DATABASE: cm_app_db
MONGODB_USERNAME: cm_app_user
MONGODB_PASSWORD: Yx4Kp9Mq7Nv2Wr8Zt6Bm3Lp1Hj5Qw9

# Replica Set Configuration
MONGODB_REPLICA_SET_NAME: rs0

# Connection Strings
MONGODB_CONNECTION_STRING_INTERNAL: mongodb://cm_app_user:<EMAIL>:27017/cm_app_db
MONGODB_CONNECTION_STRING_ADMIN: mongodb://admin:<EMAIL>:27017/admin

# External Access
MONGODB_EXTERNAL_IP: ************
MONGODB_EXTERNAL_PORT: 27017
MONGODB_NODEPORT: 32017
MONGODB_CONNECTION_STRING_EXTERNAL: *********************************************************************************
MONGODB_CONNECTION_STRING_EXTERNAL_ADMIN: ***********************************************************************

# MongoDB Express Web UI
MONGODB_EXPRESS_URL: http://<node-ip>:32081
MONGODB_EXPRESS_USERNAME: admin
MONGODB_EXPRESS_PASSWORD: MongoExpress123!

# Status: ✅ AUTHENTICATION ENABLED
# Deployment Date: 2025-05-29
# External Access: ✅ WORKING on ************:27017

# Notes:
# - ✅ Authentication is ENABLED and working
# - ✅ External access on ************:27017 is functional
# - Change these passwords before production use
# - Store these credentials securely
# - Use Kubernetes secrets for sensitive data
# - Consider enabling SSL/TLS for production

# Test Commands:
# External connection test:
# mongosh "***********************************************************************"
#
# Application connection test:
# mongosh "*********************************************************************************"
