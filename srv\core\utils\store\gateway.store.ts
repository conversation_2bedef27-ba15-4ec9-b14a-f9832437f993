import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ess, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from "cm_net";
import type { SaltedPassword } from "../user/password";

export interface DeviceSession {
    id: string;
    name?: string;
    online: boolean;
    lastOnline: Date;
    deviceType?: string;

    [k: string]: any;
}

export interface StoredUser extends User {
    pass?: SaltedPassword;
}

export abstract class GatewayStore {
    // -------------------------------- lifecycle
    abstract connect(): Promise<void>;
    abstract disconnect(): Promise<void>;

    // -------------------------------- user
    abstract userGetOrCreateByEmail(
        props: Pick<User, "name" | "pic"> & { email: string },
    ): Promise<StoredUser>;
    abstract userGetById(id: string): Promise<StoredUser | undefined>;
    abstract userSet(user: Partial<StoredUser> & { id: string }): Promise<void>;

    // -------------------------------- project
    abstract projectGet(id: string): Promise<Project | undefined>;
    abstract projectSet(
        project: Partial<Project> & { id: string },
    ): Promise<void>;
    abstract projectDel(id: string): Promise<void>;

    // -------------------------------- device session
    abstract sessionGet(sid: string): Promise<DeviceSession>;
    abstract sessionSet(
        session: Partial<DeviceSession> & { id: string },
    ): Promise<void>;
    abstract sessionDel(sid: string): Promise<void>;

    // -------------------------------- access
    abstract userDeviceAdd(
        uid: string,
        sid: string,
        name: string,
    ): Promise<void>;
    abstract userDeviceDel(uid: string, sid: string): Promise<void>;
    abstract userDeviceList(uid: string): Promise<DeviceAccess[]>;
    abstract deviceUserList(sid: string): Promise<DeviceAccess[]>;

    // -------------------------------- query
    abstract deviceGetUsage(sid: string): Promise<SysUsage | undefined>;

    async listUserDevices(
        uid: string,
        opts?: { withUsage?: false },
    ): Promise<Device[]> {
        const devices = await this.userDeviceList(uid);
        return Promise.all(
            devices.map(async (da) => {
                const s = await this.sessionGet(da.sid);

                const d = {
                    id: da.sid,
                    name: s?.name ?? da.name,
                    online: s?.online ?? false,
                    lastOnline: s?.lastOnline,
                    deviceType: s.deviceType,
                } as Device;

                // if (d && opts?.withUsage !== false) {
                if (d) {
                    const usage = await this.deviceGetUsage(da.sid);
                    d.usage = usage;
                }

                return d;
            }),
        ).then((devices) => devices.filter(Boolean) as Device[]);
    }

    async listDeviceUsers(sid: string): Promise<User[]> {
        const users = await this.deviceUserList(sid);
        return Promise.all(
            users.map(async (d) => {
                const u = await this.userGetById(d.uid);

                return {
                    id: d.uid,
                    name: u?.name,
                    pic: u?.pic,
                } as User;
            }),
        );
    }

    async userGetName(uid: string | undefined): Promise<string> {
        if (!uid) return "<stranger>";
        const user = await this.userGetById(uid);
        return user?.name ?? "<stranger>";
    }

    async deviceGetName(sid: string): Promise<string> {
        const session = await this.sessionGet(sid);
        return session?.name ?? `<device:${sid}>`;
    }
}
