import { $pick, SDB, type <PERSON>BProps } from "cm_sdb";
import {
    type <PERSON>ceSession,
    GatewayStore,
    type StoredUser,
} from "./gateway.store";
import { nanoid } from "nanoid";
import {
    $err,
    $ref,
    type DeviceAccess,
    id_c,
    type Msg,
    type Msg<PERSON>istOpts,
    type Project,
    type ProjectAccess,
    type Room,
    type RoomListOpts,
    type RoomRole,
    type RoomUser,
    type RoomWithRole,
    type SysUsage,
    type User,
} from "cm_net";
import { peers } from "../../ws.handler/ws.handler";

export class GatewayStoreSDB extends GatewayStore {
    async connect(): Promise<void> {
    }
    async disconnect(): Promise<void> {
    }

    readonly sdb: SDB;

    constructor(props: SDBProps) {
        super();
        this.sdb = new SDB(props);
    }

    async userGetOrCreateByEmail(
        props: Pick<User, "name" | "pic"> & { email: string },
    ): Promise<User> {
        // -------------------------------------- check if user exists
        const user = await this.userGetByEmail(props.email);
        if (user) return user;

        // -------------------------------------- create ne user
        return await this.sdb.put("user", {
            id: nanoid(16),
            ...props,
        });
    }

    async userGetById(id: string): Promise<StoredUser | undefined> {
        const user = await this.sdb.get<StoredUser>("user", id);
        return user;
    }

    async userGetByEmail(email: string): Promise<StoredUser | undefined> {
        const user = (await this.sdb.q<StoredUser>({
            tbl: "user",
            filter: {
                email: email,
            },
        }))[0];

        return user;
    }

    userSet(user: Partial<StoredUser> & { id: string }): Promise<void> {
        user = $pick(user, ["id", "name", "email", "pic"]);
        return this.sdb.put("user", {
            id: user.id,
        }, {
            merge: true,
        });
    }

    // ----------------------------------------- project
    projectGet(id: string): Promise<Project | undefined> {
        return this.sdb.get<Project>("project", id);
    }
    projectSet(project: Partial<Project> & { id: string }): Promise<void> {
        return this.sdb.put("project", {
            ...project,
            id: project.id,
        }, {
            merge: true,
        });
    }
    projectDel(id: string): Promise<void> {
        return this.sdb.del("project", id);
    }

    async projectList(uid: string): Promise<Project[]> {
        return (await this.sdb.q<Project>({
            tbl: "project_user",
            filter: {
                id: {
                    $gte: $ref("project_user", `${uid}|`),
                    $lt: $ref("project_user", `${uid}|\uffff`),
                },
            },
        }));
    }

    async projectUserSet(
        pid: string,
        uid: string,
        role: string | null,
    ): Promise<void> {
        return this.sdb.put("project_user", {
            id: `${pid}|${uid}`,
            pid: pid,
            uid: uid,
        });
    }

    async projectUserList(pid: string) {
        return (await this.sdb.q<ProjectAccess>({
            tbl: "project_user",
            filter: {
                id: {
                    $gte: $ref("project_user", `${pid}|`),
                    $lt: $ref("project_user", `${pid}|\uffff`),
                },
            },
        }));
    }

    async userProjectList(uid: string) {
        return (await this.sdb.q<ProjectAccess>({
            tbl: "project_user",
            filter: {
                uid: uid,
            },
        }));
    }

    async projectUserHasAccess(uid: string, pid: string): Promise<boolean> {
        const access = await this.sdb.get("project_user", `${pid}|${uid}`);

        return typeof access !== "undefined";
    }

    // ----------------------------------------- session
    async sessionGet(sid: string): Promise<DeviceSession> {
        let session = await this.sdb.get<DeviceSession>("session", sid);

        if (!session) {
            session = {
                id: sid,
                online: true,
                lastOnline: new Date(),
            };
            await this.sdb.put("session", {
                id: session.id,
            }, { merge: true });
        } else {
            session.online = true;
            session.lastOnline = new Date();
            await this.sdb.put("session", session, { merge: true });
        }

        return session;
    }

    async sessionSet(
        session: Partial<DeviceSession> & { id: string },
    ): Promise<void> {
        return this.sdb.put("session", {
            ...session,
            id: session.id,
        }, { merge: true });
    }

    async sessionDel(sid: string): Promise<void> {
        return this.sdb.del("session", sid);
    }

    async userDeviceAdd(uid: string, sid: string, name: string): Promise<void> {
        const id = `${sid}|${uid}`;

        return await this.sdb.put("user_device", {
            id: id,
            uid: uid,
            sid: sid,
            name: name,
        });
    }

    async userDeviceDel(uid: string, sid: string): Promise<void> {
        const id = `${sid}|${uid}`;
        return await this.sdb.del("user_device", id);
    }

    async userDeviceList(uid: string): Promise<DeviceAccess[]> {
        return (await this.sdb.q<DeviceAccess>({
            tbl: "user_device",
            filter: {
                uid: uid,
            },
        }));
    }

    async deviceUserList(sid: string): Promise<DeviceAccess[]> {
        return (await this.sdb.q<DeviceAccess>({
            tbl: "user_device",
            filter: {
                id: {
                    $gte: $ref("user_device", `${sid}|`),
                    $lt: $ref("user_device", `${sid}|\uffff`),
                },
            },
        }));
    }

    // ------------------------------------------ msg
    async msgPut(
        msg: Msg,
        opts?: {
            autoCreateRoom?: false;
            checkAccess?: boolean
        },
    ): Promise<void> {
        const ts = msg.ts = new Date();
        if (!msg.rid) throw $err.err("msg-no-rid", "rid required");
        if (!msg.uid) throw $err.err("msg-no-uid", "uid required");

        // create msg id
        msg.id = `${msg.rid}|${id_c(ts)}`;
        let roomCreated: boolean = false;

        // get room
        let room: Room | undefined = await this.roomGet(msg.rid);
        let hasAccess: boolean = false;

        // if not set to false
        if (!room && opts?.autoCreateRoom !== false) {
            hasAccess = true;
            roomCreated = true;

            // create room
            await this.roomPut({
                id: msg.rid,
                // type: "chat",

                lMsg: {
                    id: msg.id,
                    txt: msg.txt,
                    ts: msg.ts,

                    uid: msg.uid,
                }
            });

            // add user to room
            await this.roomUserSet(msg.rid, msg.uid, "owner");
        } else if (room) {
            hasAccess = await this.roomUserHasAccess(msg.uid, msg.rid);
        }

        if (opts?.checkAccess && !hasAccess) {
            throw $err.err("msg-no-access", "No access to room");
        }

        // put into msg table
        await this.sdb.put("m", msg);

        // check if user has room
        // const room = await this.roomUserHasAccess(msg.rid) // update room
        if (roomCreated) {
            await this.sdb.put("room", {
                id: msg.rid,
                lMsgId: msg.id,
                lMsgTxt: msg.txt,
                lMsgTs: msg.ts,
            }, {
                merge: true,
            });
        }

        // notify all users in room
    }

    async msgList(opts: MsgListOpts): Promise<Msg[]> {
        const msgs = await this.sdb.q<Msg>({
            tbl: "m",
            filter: {
                rid: opts.rid,
                deviceId: opts.deviceId,
            },
            sort: { id: -1 },
            limit: opts.limit ?? 30,
        });

        return msgs;
    }

    async roomList(
        uid: string,
        opts: RoomListOpts = {},
    ): Promise<RoomWithRole[]> {
        const userRooms = await this.sdb.q<RoomUser>({
            tbl: "room_user",

            // debug: true,
            filter: {
                // type: { $eq: opts.type },
                id: {
                    $gte: $ref("room_user", `${uid}:`),
                    $lt: $ref("room_user", `${uid}:\uffff`),
                },
            },
        });

        const result: RoomWithRole[] = [];
        await Promise.all(userRooms.map(async (ur: RoomUser) => {
            const room = await this.roomGet(ur.id);
            if (!room) return;

            const r: RoomWithRole = {
                ...room,
                role: ur.role,
            };

            if (ur.alias) r.name = ur.alias;

            // if no name, get tgt name
            if (!r.name && ur.uid) {
                r.name = await this.userGetName(ur.uid);
            } else {
                r.name = 'New Group';
            }

            result.push(r);
        }));

        return result;
    }

    async roomUserHasAccess(uid: string, rid: string): Promise<boolean> {
        const access = await this.sdb.get("room_user", `${rid}:${uid}`);

        return typeof access !== "undefined";
    }

    async roomGet(id: string): Promise<Room | undefined> {
        return (await this.sdb.get<Room>("room", id));
    }

    async roomPut(room: Room): Promise<void> {
        return (await this.sdb.put("room", room));
    }

    async roomUserSet(rid: string, uid: string, role: RoomRole | null) {
        const id = `${rid}:${uid}`;

        if (role) {
            await this.sdb.put("room_user", {
                id: id,
                rid: rid,
                uid: uid,
                role: role,
            });
        } else {
            await this.sdb.del("room_user", id);
        }

        // notify all users in room
        
    }

    async deviceGetUsage(sid: string): Promise<SysUsage | undefined> {
        const peersBySid = peers[sid];
        if (!peersBySid) return undefined;

        // get first peer
        const peer = [...peersBySid][0];
        if (!peer) return undefined;
        return peer?.sysUsage;
    }
}
