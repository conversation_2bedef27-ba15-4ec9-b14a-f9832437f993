# Redis Kubernetes Deployment

This directory contains Kubernetes manifests for deploying Redis with Ceph persistent storage.

## Files Overview

- `namespace.yaml` - Creates the Redis namespace
- `secret.yaml` - Contains Redis authentication secrets
- `configmap.yaml` - Redis configuration file
- `pvc.yaml` - Persistent Volume Claims using Ceph storage
- `deployment.yaml` - Redis deployment configuration
- `service.yaml` - ClusterIP and NodePort services
- `deploy-redis.sh` - Automated deployment script
- `credentials.yaml` - Credentials reference (DO NOT COMMIT)

## Quick Deployment

```bash
# Make the deployment script executable
chmod +x .scripts/deployment/databases/redis/deploy-redis.sh

# Deploy Redis
./.scripts/deployment/databases/redis/deploy-redis.sh
```

## Manual Deployment

```bash
# Apply manifests in order
kubectl apply -f .scripts/deployment/databases/redis/namespace.yaml
kubectl apply -f .scripts/deployment/databases/redis/secret.yaml
kubectl apply -f .scripts/deployment/databases/redis/configmap.yaml
kubectl apply -f .scripts/deployment/databases/redis/pvc.yaml
kubectl apply -f .scripts/deployment/databases/redis/deployment.yaml
kubectl apply -f .scripts/deployment/databases/redis/service.yaml
```

## Access Information

### Internal Cluster Access
- **Service**: `redis.redis.svc.cluster.local:6379`
- **Connection String**: `redis://:<EMAIL>:6379`

### External Access (NodePort)
- **Port**: `32379`
- **Connection String**: `redis://:PASSWORD@<node-ip>:32379`

## Credentials

- **Admin Password**: `Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8`
- **App Password**: `Ap7Mx4Kp9Nv2Wr8Zt6Bm3Lp1Hj5Qw9`

## Testing

```bash
# Connect to Redis CLI
kubectl exec -it deployment/redis -n redis -- redis-cli -a Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8

# Test connection
kubectl exec -it deployment/redis -n redis -- redis-cli -a Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8 ping

# Get Redis info
kubectl exec -it deployment/redis -n redis -- redis-cli -a Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8 info

# Monitor Redis commands
kubectl exec -it deployment/redis -n redis -- redis-cli -a Rd5Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8 monitor
```

## Storage

- **Data Volume**: 20Gi (Ceph RBD)
- **Config Volume**: 1Gi (Ceph RBD)
- **Storage Class**: `rook-ceph-block`

## Configuration

Redis is configured with:
- Password authentication enabled
- Persistence enabled (RDB + AOF)
- Memory policy: `allkeys-lru`
- 16 databases available
- Slow query logging enabled

## Monitoring

Check deployment status:
```bash
kubectl get pods -n redis
kubectl get pvc -n redis
kubectl get svc -n redis
```

## Cleanup

```bash
kubectl delete namespace redis
```

This will remove all Redis resources including persistent volumes.
