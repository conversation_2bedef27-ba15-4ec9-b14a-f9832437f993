import { $err } from "cm_net";
import { $fn } from "../../../../core/fn";
import { gatewayStore } from "../../../../../instance";

$fn("auth.emer", async function (email: string, name: string, pic: string) {
    if (!email || !name) {
        throw $err.err("user-unverified", "Google profile is not verified");
    }

    const user = await gatewayStore.userGetOrCreateByEmail({
        email: email,
        name: name,
        pic: pic,
    });

    this.user.uid = user.id;
});
