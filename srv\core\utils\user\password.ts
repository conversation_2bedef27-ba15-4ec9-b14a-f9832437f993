import * as crypto from 'crypto';
import { promisify } from 'util';

// Create promisified versions of crypto functions
const randomBytes = promisify(crypto.randomBytes);
const pbkdf2 = promisify(crypto.pbkdf2);

export interface SaltedPassword {
    salt: string;
    hash: string;
}

export async function saltPassword(
    pass: string,
    salt?: string,
): Promise<SaltedPassword> {
    // Generate a random salt if not provided
    if (!salt) {
        const saltBuffer = await randomBytes(16);
        salt = saltBuffer.toString('hex');
    }

    // Create a hash using PBKDF2 with 1000 iterations and SHA-512
    const hashBuffer = await pbkdf2(
        pass,
        salt,
        1000,
        64,
        'sha512'
    );
    const hash = hashBuffer.toString('hex');

    return {
        salt,
        hash
    };
}

export async function checkSaltedPassword(
    pass: string,
    salted: SaltedPassword,
): Promise<boolean> {
    // Hash the provided password with the stored salt
    const hashBuffer = await pbkdf2(
        pass,
        salted.salt,
        1000,
        64,
        'sha512'
    );
    const hashedPassword = hashBuffer.toString('hex');

    // Compare the generated hash with the stored hash
    return hashedPassword === salted.hash;
}