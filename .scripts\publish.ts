import { $ } from "bun";

// process.chdir('..');

// Build Server
await $`bun run build`;

// Build and push Docker image
await $`docker build -t docker.alienai.id/chitosa/cm-gateway .`;
await $`docker push docker.alienai.id/chitosa/cm-gateway`;

// Deploy
await $`kubectl apply -f .scripts/cm-gateway.yaml`;
await $`kubectl rollout restart deployment cm-gateway -n cm-gateway`;

console.log('Done Publishing at ', new Date().toLocaleString());